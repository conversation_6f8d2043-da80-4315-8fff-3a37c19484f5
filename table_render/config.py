"""
配置模型定义

使用 Pydantic 定义项目的配置结构，确保配置的验证和类型安全。
"""

from pydantic import BaseModel, Field, field_validator
from typing import Literal, Union, Optional, List, Dict, Any
import os


class DebugConfig(BaseModel):
    """调试配置"""
    enable_debug: bool = Field(default=False, description="是否启用调试模式")
    save_intermediate_stages: bool = Field(default=True, description="是否保存中间处理阶段的图像")
    debug_output_dir: str = Field(default="./debug_output", description="调试输出目录")

class OutputConfig(BaseModel):
    """输出配置"""
    output_dir: str = Field(default="./output/", description="输出目录路径")
    label_suffix: Optional[str] = Field(default=None, description="标注文件后缀，用于区分图像和标注文件")


# ============================================================================
# V3.4 新增：概率化配置模型
# ============================================================================

class ProbabilisticRange(BaseModel):
    """定义一个概率化的数值范围列表"""
    range_list: List[List[Union[int, float]]] = Field(description="数值范围列表，每个元素为[min, max]格式")
    probability_list: List[float] = Field(description="对应范围的概率列表")

    @field_validator('probability_list')
    @classmethod
    def validate_probabilities(cls, v, info):
        """验证概率列表"""
        if info.data and 'range_list' in info.data and len(v) != len(info.data['range_list']):
            raise ValueError("概率列表长度必须与范围列表长度相同")
        if any(p < 0 for p in v):
            raise ValueError("概率值不能为负数")
        return v


class ProbabilisticOptions(BaseModel):
    """定义一个概率化的离散选项列表"""
    option_list: List[Any] = Field(description="选项列表")
    probability_list: List[float] = Field(description="对应选项的概率列表")

    @field_validator('probability_list')
    @classmethod
    def validate_probabilities(cls, v, info):
        """验证概率列表"""
        if info.data and 'option_list' in info.data and len(v) != len(info.data['option_list']):
            raise ValueError("概率列表长度必须与选项列表长度相同")
        if any(p < 0 for p in v):
            raise ValueError("概率值不能为负数")
        return v


class RangeConfig(BaseModel):
    """整数范围配置，用于表示 min-max 范围"""
    min: int = Field(ge=1, description="最小值")
    max: int = Field(ge=1, description="最大值")

    @field_validator('max')
    @classmethod
    def validate_max_ge_min(cls, v, info):
        """验证最大值不小于最小值"""
        if info.data and 'min' in info.data and v < info.data['min']:
            raise ValueError(f"max ({v}) must be >= min ({info.data['min']})")
        return v


class FloatRangeConfig(BaseModel):
    """浮点数范围配置，用于表示 min-max 范围"""
    min: float = Field(ge=0.0, description="最小值")
    max: float = Field(ge=0.0, description="最大值")

    @field_validator('max')
    @classmethod
    def validate_max_ge_min(cls, v, info):
        """验证最大值不小于最小值"""
        if info.data and 'min' in info.data and v < info.data['min']:
            raise ValueError(f"max ({v}) must be >= min ({info.data['min']})")
        return v


class BorderConfig(BaseModel):
    """边框配置"""
    top: bool = Field(default=True, description="顶部边框")
    right: bool = Field(default=True, description="右侧边框")
    bottom: bool = Field(default=True, description="底部边框")
    left: bool = Field(default=True, description="左侧边框")


class BorderModeConfig(BaseModel):
    """V3.2新增：边框模式配置"""
    mode: Literal['full', 'none', 'semi'] = Field(default='full', description="边框模式：full(有线)、none(纯无线)、semi(半无线)")

    # 半无线模式的详细配置
    semi_config: Optional['SemiBorderConfig'] = Field(default=None, description="半无线模式配置")


class BorderModeOption(BaseModel):
    """V3.4新增：单个边框模式的配置及其概率"""
    probability: float = Field(ge=0.0, description="该边框模式的概率")
    config: Dict[str, Any] = Field(description="边框模式的具体配置")


class ProbabilisticBorderConfig(BaseModel):
    """V3.4新增：概率化边框配置"""
    mode_options: List[BorderModeOption] = Field(description="边框模式选项列表")

    @field_validator('mode_options')
    @classmethod
    def validate_mode_options(cls, v):
        """验证边框模式选项"""
        if not v:
            raise ValueError("边框模式选项列表不能为空")

        # 检查概率值
        total_prob = sum(option.probability for option in v)
        if total_prob <= 0:
            raise ValueError("所有边框模式的概率总和必须大于0")

        return v


class SemiBorderConfig(BaseModel):
    """半无线边框配置"""
    row_line_probability: float = Field(default=0.5, description="行线出现概率")
    col_line_probability: float = Field(default=0.5, description="列线出现概率")
    outer_frame: bool = Field(default=True, description="是否保留外框")
    header_separator: bool = Field(default=True, description="是否强制保留表头分割线")


# 前向引用解决
try:
    BorderModeConfig.model_rebuild()
except:
    pass  # 在某些情况下可能不需要rebuild


class ColorContrastConfig(BaseModel):
    """V3.3优化：颜色对比度配置"""
    min_contrast_ratio: float = Field(default=4.5, description="最小对比度比例（WCAG AA标准）")
    use_soft_colors_probability: float = Field(default=0.7, ge=0.0, le=1.0, description="使用柔和颜色的概率")


class SizingConfigItem(BaseModel):
    """单个sizing配置项"""
    name: str = Field(description="配置名称，用于日志和调试")
    probability: float = Field(ge=0.0, le=1.0, description="配置启用概率")
    type: Literal["specific", "probabilistic"] = Field(description="配置类型：specific(指定特定行/列)或probabilistic(概率触发)")
    target_rows: Optional[List[int]] = Field(default=None, description="目标行索引列表（仅specific类型使用）")
    target_cols: Optional[List[int]] = Field(default=None, description="目标列索引列表（仅specific类型使用）")
    per_row_probability: Optional[float] = Field(default=None, ge=0.0, le=1.0, description="每行触发概率（仅probabilistic类型使用）")
    per_col_probability: Optional[float] = Field(default=None, ge=0.0, le=1.0, description="每列触发概率（仅probabilistic类型使用）")
    height_range: Optional[List[int]] = Field(default=None, description="行高范围[min, max]")
    width_range: Optional[List[int]] = Field(default=None, description="列宽范围[min, max]")

    @field_validator('height_range')
    @classmethod
    def validate_height_range(cls, v, info):
        """验证行高范围配置"""
        if not info.data:
            return v

        config_type = info.data.get('type')

        if config_type == 'specific' and info.data.get('target_rows') and v is None:
            raise ValueError("指定target_rows时必须提供height_range")
        elif config_type == 'probabilistic' and info.data.get('per_row_probability') and v is None:
            raise ValueError("指定per_row_probability时必须提供height_range")

        return v

    @field_validator('width_range')
    @classmethod
    def validate_width_range(cls, v, info):
        """验证列宽范围配置"""
        if not info.data:
            return v

        config_type = info.data.get('type')

        if config_type == 'specific' and info.data.get('target_cols') and v is None:
            raise ValueError("指定target_cols时必须提供width_range")
        elif config_type == 'probabilistic' and info.data.get('per_col_probability') and v is None:
            raise ValueError("指定per_col_probability时必须提供width_range")

        return v




class RowColumnSizeConfig(BaseModel):
    """行列尺寸配置"""
    # 全局默认配置
    default_row_height: Union[str, int, RangeConfig] = Field(default='auto', description="默认行高，可以是'auto'、固定值或范围")
    default_col_width: Union[str, int, RangeConfig] = Field(default='auto', description="默认列宽，可以是'auto'、固定值或范围")

    # 可变行高列宽配置
    row_configs: Optional[List[SizingConfigItem]] = Field(default=None, description="行级sizing配置列表")
    col_configs: Optional[List[SizingConfigItem]] = Field(default=None, description="列级sizing配置列表")

    # V3.4向后兼容：保留原有字段
    row_height: Optional[Union[str, int, RangeConfig]] = Field(default=None, description="行高（向后兼容字段）")
    col_width: Optional[Union[str, int, RangeConfig]] = Field(default=None, description="列宽（向后兼容字段）")

    @field_validator('row_height')
    @classmethod
    def handle_legacy_row_height(cls, v, info):
        """处理向后兼容的row_height字段"""
        if v is not None and info.data:
            # 如果使用了旧字段，自动设置到对应的default字段
            info.data['default_row_height'] = v
        return v

    @field_validator('col_width')
    @classmethod
    def handle_legacy_col_width(cls, v, info):
        """处理向后兼容的col_width字段"""
        if v is not None and info.data:
            # 如果使用了旧字段，自动设置到对应的default字段
            info.data['default_col_width'] = v
        return v


class FontDirectoryConfig(BaseModel):
    """V3.3新增：带概率的字体目录配置"""
    path: str = Field(description="字体目录路径")
    probability: float = Field(default=1.0, ge=0.0, le=1.0, description="选择概率")


class FontConfig(BaseModel):
    """字体配置"""
    font_dirs: Union[str, List[str]] = Field(
        default="./assets/fonts/",
        description="字体文件目录，可以是单个目录或目录列表"
    )
    font_dir_probabilities: Optional[List[float]] = Field(
        default=None,
        description="字体目录选择概率，与font_dirs对应。如果为None则等概率选择"
    )
    default_family: Union[str, List[str], ProbabilisticOptions] = Field(default="Arial", description="默认字体族，可以是单个字体、字体列表或概率化选项")
    default_size: Union[int, RangeConfig, ProbabilisticRange] = Field(default=14, description="默认字体大小，可以是固定值、范围或概率化范围")
    bold_probability: Union[float, FloatRangeConfig] = Field(default=0.1, description="粗体概率，可以是固定值或范围")
    italic_probability: Union[float, FloatRangeConfig] = Field(default=0.1, description="斜体概率，可以是固定值或范围")
    fallback_font: str = Field(default="Microsoft YaHei", description="默认回退字体")


class ComplexHeaderConfig(BaseModel):
    """复杂表头配置"""
    enabled: bool = Field(default=False, description="是否启用复杂表头")
    structure: Optional[List[List[Dict[str, Any]]]] = Field(default=None, description="复杂表头结构定义")


class StructureConfig(BaseModel):
    """表格结构配置"""
    body_rows: Union[int, RangeConfig, ProbabilisticRange] = Field(default=5, description="表体行数，可以是固定值、范围或概率化范围")
    cols: Union[int, RangeConfig, ProbabilisticRange] = Field(default=4, description="表格列数，可以是固定值、范围或概率化范围")
    header_rows: Union[int, RangeConfig, ProbabilisticRange] = Field(default=1, description="表头行数，可以是固定值、范围或概率化范围")
    merge_probability: Union[float, FloatRangeConfig, ProbabilisticRange] = Field(default=0.0, description="单元格合并概率，可以是固定值、范围或概率化范围")
    max_row_span: Union[int, RangeConfig, ProbabilisticOptions] = Field(default=2, description="最大行跨度，可以是固定值、范围或概率化选项")
    max_col_span: Union[int, RangeConfig, ProbabilisticOptions] = Field(default=2, description="最大列跨度，可以是固定值、范围或概率化选项")
    complex_header: Optional[ComplexHeaderConfig] = Field(default=None, description="复杂表头配置")


class BlankControlConfig(BaseModel):
    """空白控制配置"""
    wired_trigger_probability: float = Field(
        default=0.01,
        ge=0.0,
        le=1.0,
        description="有线模式(border_mode=full)下行/列被标记为可空白的概率"
    )
    wireless_trigger_probability: float = Field(
        default=0.005,
        ge=0.0,
        le=1.0,
        description="无线模式(border_mode=none/semi)下行/列被标记为可空白的概率"
    )
    cell_blank_probability: float = Field(
        default=0.3,
        ge=0.0,
        le=1.0,
        description="在标记的行/列中，单元格真正变空的概率"
    )

    # 向后兼容：保留旧的trigger_probability字段
    trigger_probability: Optional[float] = Field(
        default=None,
        ge=0.0,
        le=1.0,
        description="向后兼容字段，如果设置则同时应用于有线和无线模式"
    )


class CSVSourceConfig(BaseModel):
    """CSV数据源配置"""
    # 向后兼容：单文件模式
    file_path: Optional[str] = Field(default=None, description="CSV文件路径（单文件模式）")

    # V5.0新增：概率化目录选择
    csv_dirs: Optional[List[str]] = Field(
        default=None,
        description="CSV目录列表，支持概率化选择"
    )
    csv_dir_probabilities: Optional[List[float]] = Field(
        default=None,
        description="CSV目录选择概率，与csv_dirs对应。如果为None则等概率选择"
    )

    # 采样模式配置
    sampling_mode: Literal['random', 'positional'] = Field(
        default='random',
        description="内容填充模式：random=随机采样（行列对应），positional=位置对应"
    )

    # V5.0新增：空白控制配置
    blank_control: Optional['BlankControlConfig'] = Field(
        default=None,
        description="空白单元格控制配置"
    )

    # 原有配置保持
    encoding: str = Field(default='utf-8', description="文件编码")
    mismatch_strategy: Literal['truncate', 'fill_empty'] = Field(
        default='truncate',
        description="当CSV维度与表格结构不匹配时的处理策略（仅在positional模式下有效）"
    )

    @field_validator('csv_dir_probabilities')
    @classmethod
    def validate_csv_dir_probabilities(cls, v, info):
        """验证CSV目录概率配置"""
        if v is not None and info.data and 'csv_dirs' in info.data:
            csv_dirs = info.data['csv_dirs']
            if csv_dirs and len(v) != len(csv_dirs):
                raise ValueError(f"csv_dir_probabilities长度({len(v)})必须与csv_dirs长度({len(csv_dirs)})相等")
            if any(p < 0 for p in v):
                raise ValueError("csv_dir_probabilities中的概率值不能为负数")
        return v

    @field_validator('csv_dirs')
    @classmethod
    def validate_csv_config(cls, v, info):
        """验证CSV配置的完整性"""
        if info.data:
            file_path = info.data.get('file_path')
            if not v and not file_path:
                raise ValueError("必须配置csv_dirs或file_path中的至少一个")
        return v


class ContentConfig(BaseModel):
    """内容生成配置"""
    source_type: Literal['programmatic', 'csv'] = Field(
        default='programmatic',
        description="内容来源类型"
    )
    programmatic_types: Optional[List[str]] = Field(
        default=['date', 'currency', 'percentage'],
        description="程序化生成的数据类型列表"
    )
    csv_source: Optional[CSVSourceConfig] = Field(
        default=None,
        description="CSV数据源配置，当source_type为csv时必需"
    )


class CellStyleOverride(BaseModel):
    """单元格样式覆盖"""
    font_family: Optional[str] = None
    font_size: Optional[int] = None
    font_bold: Optional[bool] = None
    font_italic: Optional[bool] = None
    text_color: Optional[str] = None
    background_color: Optional[str] = None
    horizontal_align: Optional[Literal['left', 'center', 'right']] = None
    vertical_align: Optional[Literal['top', 'middle', 'bottom']] = None


class HierarchicalStyleConfig(BaseModel):
    """分层样式配置"""
    column_styles: Optional[Dict[int, CellStyleOverride]] = Field(default=None, description="列级样式覆盖")
    row_styles: Optional[Dict[int, CellStyleOverride]] = Field(default=None, description="行级样式覆盖")
    cell_styles: Optional[Dict[str, CellStyleOverride]] = Field(default=None, description="单元格级样式覆盖")


class StyleInheritanceConfig(BaseModel):
    """V3.3样式继承配置"""
    font_family_change_probability: float = Field(default=0.2, description="字体族变化概率")
    font_size_change_probability: float = Field(default=0.3, description="字体大小变化概率")
    alignment_change_probability: float = Field(default=0.4, description="对齐方式变化概率")
    padding_change_probability: float = Field(default=0.3, description="内边距变化概率")

    # V3.3新增：独立的文本色和背景色变化概率
    text_color_change_probability: float = Field(default=0.3, description="文本颜色变化概率")
    background_color_change_probability: float = Field(default=0.2, description="背景颜色变化概率")


class CommonStyleConfig(BaseModel):
    """V3.3精简：公共样式配置"""
    font: FontConfig = Field(default_factory=FontConfig)
    horizontal_align: Union[List[Literal['left', 'center', 'right']], ProbabilisticOptions] = Field(
        default=['left', 'center', 'right'],
        description="水平对齐方式列表或概率化选项"
    )
    vertical_align: Union[List[Literal['top', 'middle', 'bottom']], ProbabilisticOptions] = Field(
        default=['top', 'middle', 'bottom'],
        description="垂直对齐方式列表或概率化选项"
    )
    padding: Union[int, RangeConfig, ProbabilisticRange] = Field(default=5, description="内边距，可以是固定值、范围或概率化范围")

    # V3.3新特性：颜色生成的完全概率化
    randomize_color_probability: float = Field(default=0.3, ge=0.0, le=1.0, description="颜色随机化概率")

    # V4.5新特性：线条颜色随机化控制
    randomize_border_color_probability: float = Field(default=0.0, ge=0.0, le=1.0, description="线条颜色随机化概率")

    # 合并单元格对齐优化配置
    merged_cell_center_probability: float = Field(default=0.0, ge=0.0, le=1.0, description="合并单元格相对居中对齐的概率")

    # 颜色对比度配置
    color_contrast: ColorContrastConfig = Field(default_factory=ColorContrastConfig)


class BaseStyleConfig(BaseModel):
    """基础样式配置，包含所有可被概率化的样式属性"""
    font: FontConfig = Field(default_factory=FontConfig)
    horizontal_align: List[Literal['left', 'center', 'right']] = Field(
        default=['left', 'center', 'right'],
        description="水平对齐方式列表"
    )
    vertical_align: List[Literal['top', 'middle', 'bottom']] = Field(
        default=['top', 'middle', 'bottom'],
        description="垂直对齐方式列表"
    )
    padding: Union[int, RangeConfig] = Field(default=5, description="内边距")

    # V3.3新特性：颜色生成的完全概率化
    randomize_color_probability: float = Field(default=0.3, ge=0.0, le=1.0, description="颜色随机化概率")

    # V4.5新特性：线条颜色随机化控制
    randomize_border_color_probability: float = Field(default=0.0, ge=0.0, le=1.0, description="线条颜色随机化概率")


class StyleConfig(BaseModel):
    """V3.3样式配置：精简的样式继承机制"""
    # 公共样式配置（作为继承基础）
    common: CommonStyleConfig = Field(default_factory=CommonStyleConfig, description="公共样式配置")

    # 样式继承配置
    inheritance: StyleInheritanceConfig = Field(default_factory=StyleInheritanceConfig, description="样式继承配置")

    # 边框模式配置 - V3.4支持概率化
    border_mode: Union[BorderModeConfig, ProbabilisticBorderConfig] = Field(default_factory=BorderModeConfig, description="边框模式配置，支持固定模式或概率化模式")

    # 全局样式配置
    zebra_stripes: Union[bool, float] = Field(default=False, description="斑马条纹配置：bool(向后兼容)或float(概率值)")
    sizing: RowColumnSizeConfig = Field(default_factory=RowColumnSizeConfig)
    hierarchical: Optional[HierarchicalStyleConfig] = Field(default=None, description="分层样式配置")

    # V4.0新增：内容溢出处理策略
    overflow_strategy: Literal['truncate', 'wrap'] = Field(default='wrap', description="内容溢出处理策略：truncate(截断)或wrap(换行)")


# ============================================================================
# V4.0 新增：图像后处理配置模型
# ============================================================================


class PerspectiveConfig(BaseModel):
    """透视变换效果配置"""
    probability: float = Field(default=0.0, ge=0.0, le=1.0, description="应用透视变换效果的概率")
    max_offset_ratio: Optional[float] = Field(default=0.05, ge=0.0, le=0.2, description="最大偏移比例（向后兼容）")
    range_list: Optional[List[List[float]]] = Field(default=None, description="透视变换强度范围列表")
    probability_list: Optional[List[float]] = Field(default=None, description="对应range_list的概率分布")
    content_area_shrink_ratio: float = Field(default=0.1, ge=0.0, le=0.5, description="内容区域缩小比例，用于保守地避免黑边")
    adaptive_scaling: Optional[List[List[float]]] = Field(default=None, description="动态缩放配置：[[min_table_size, max_scale_factor], [max_table_size, min_scale_factor]]")
    decay_rate: float = Field(default=1.8, ge=0.1, le=10.0, description="指数衰减率")

    @field_validator('range_list')
    @classmethod
    def validate_range_list(cls, v):
        """验证透视变换强度范围列表"""
        if v is None:
            return v

        if not isinstance(v, list) or len(v) == 0:
            raise ValueError("range_list必须是非空列表")

        for i, range_item in enumerate(v):
            if not isinstance(range_item, list) or len(range_item) != 2:
                raise ValueError(f"range_list[{i}]必须是包含两个元素的列表")

            min_offset, max_offset = range_item

            if not isinstance(min_offset, (int, float)) or not isinstance(max_offset, (int, float)):
                raise ValueError(f"range_list[{i}]的元素必须是数字")

            if min_offset < 0 or max_offset < 0:
                raise ValueError(f"透视变换强度范围{i}的值不能为负数")

            if min_offset > max_offset:
                raise ValueError(f"透视变换强度范围{i}的最小值不能大于最大值")

            if max_offset > 0.2:
                raise ValueError(f"透视变换强度范围{i}的最大值不能超过0.2")

        return v

    @field_validator('probability_list')
    @classmethod
    def validate_probability_list(cls, v, info):
        """验证概率列表"""
        if v is None:
            return v

        if not isinstance(v, list):
            raise ValueError("probability_list必须是列表")

        # 检查概率数量是否匹配范围数量
        range_list = info.data.get('range_list')
        if range_list and len(v) != len(range_list):
            raise ValueError(f"probability_list长度({len(v)})必须与range_list长度({len(range_list)})相同")

        # 检查概率值
        for i, prob in enumerate(v):
            if not isinstance(prob, (int, float)):
                raise ValueError(f"probability_list[{i}]必须是数字")
            if prob < 0:
                raise ValueError(f"probability_list[{i}]不能为负数")

        # 检查概率总和
        total_prob = sum(v)
        if total_prob == 0:
            raise ValueError("probability_list的总和不能为0")

        return v


class MarginControlConfig(BaseModel):
    """边距控制配置 - 使用统一的概率化配置格式"""
    range_list: List[List[int]] = Field(default_factory=list, description="边距范围列表：[[最小边距, 最大边距], ...]")
    probability_list: List[float] = Field(default_factory=list, description="对应的概率列表")

    @field_validator('range_list')
    @classmethod
    def validate_range_list(cls, v):
        """验证边距范围列表"""
        if not v:
            return v

        for i, margin_range in enumerate(v):
            if not isinstance(margin_range, list) or len(margin_range) != 2:
                raise ValueError(f"边距范围{i}格式错误，应为 [最小边距, 最大边距]")

            min_margin, max_margin = margin_range
            if not isinstance(min_margin, (int, float)) or not isinstance(max_margin, (int, float)):
                raise ValueError(f"边距范围{i}的值必须为数字")

            if min_margin < 0 or max_margin < 0:
                raise ValueError(f"边距范围{i}的值不能为负数")

            if min_margin > max_margin:
                raise ValueError(f"边距范围{i}的最小边距不能大于最大边距")

        return v

    @field_validator('probability_list')
    @classmethod
    def validate_probability_list(cls, v, info):
        """验证概率列表"""
        if not v:
            return v

        # 检查概率数量是否匹配范围数量
        range_list = info.data.get('range_list', [])
        if len(v) != len(range_list):
            raise ValueError("概率列表长度必须与边距范围列表长度匹配")

        # 检查概率值范围
        for i, prob in enumerate(v):
            if not isinstance(prob, (int, float)) or prob < 0 or prob > 1:
                raise ValueError(f"概率值{i}必须在0-1之间")

        # 检查概率总和（允许自动归一化，所以这里只是警告）
        total_prob = sum(v)
        if abs(total_prob - 1.0) > 1e-6:
            # 注意：这里不抛出异常，因为v3.4风格支持自动归一化
            pass

        return v


class BackgroundConfig(BaseModel):
    """背景图合成配置"""
    background_dirs: List[str] = Field(default_factory=list, description="背景图目录列表")
    background_dir_probabilities: List[float] = Field(default_factory=list, description="背景图目录选择概率")
    max_scale_factor: float = Field(default=3.0, ge=1.0, le=10.0, description="最大缩放倍数（V5.1已弃用，现在使用智能动态适应）")
    margin_control: Optional[MarginControlConfig] = Field(default=None, description="V4.2新增：边距控制配置")
    prefer_center_probability: float = Field(default=0.8, ge=0.0, le=1.0, description="偏向中心的概率")

    @field_validator('background_dir_probabilities')
    @classmethod
    def validate_probabilities(cls, v, info):
        """验证概率列表"""
        if not v:
            return v

        # 检查概率数量是否匹配目录数量
        background_dirs = info.data.get('background_dirs', [])
        if len(v) != len(background_dirs):
            raise ValueError("背景图目录概率数量必须与目录数量匹配")

        # 检查概率值范围
        if any(p < 0 or p > 1 for p in v):
            raise ValueError("概率值必须在0-1之间")

        # 检查概率总和
        if abs(sum(v) - 1.0) > 1e-6:
            raise ValueError("概率总和必须等于1.0")

        return v


class TableBlendingConfig(BaseModel):
    """V4.3新增：表格融合配置"""
    enable_transparency: bool = Field(default=False, description="是否启用表格透明度")
    default_color_transparency: float = Field(default=0.1, ge=0.0, le=1.0, description="默认色（白色/浅色）的透明度")
    meaningful_color_transparency: float = Field(default=0.7, ge=0.0, le=1.0, description="有意义颜色的透明度")


class CSSStabilityConfig(BaseModel):
    """V5.2新增：CSS渲染稳定性配置"""
    enable_validation: bool = Field(default=True, description="启用CSS渲染验证")
    enable_stability_check: bool = Field(default=True, description="启用CSS稳定性检查")
    extra_wait_time: float = Field(default=1.0, ge=0.0, le=10.0, description="额外等待时间（秒）")
    background_wait_time: float = Field(default=1.5, ge=0.0, le=10.0, description="背景图模式下的额外等待时间（秒）")


class PerformanceConfig(BaseModel):
    """V5.2新增：性能优化配置"""
    enable_parallel: bool = Field(default=False, description="是否启用样本级并行处理")
    max_workers: Union[str, int] = Field(default="auto", description="最大工作线程数，'auto'表示自动检测CPU核心数")
    max_browser_instances: int = Field(default=8, ge=1, le=32, description="最大浏览器实例数（V5.2已废弃，每线程独立实例）")

    # TurboJPEG高速图像保存配置
    enable_turbo_jpeg: bool = Field(default=True, description="是否启用TurboJPEG优化")
    turbo_jpeg_quality: int = Field(default=95, ge=1, le=100, description="JPEG质量参数(1-100)")
    turbo_jpeg_format: str = Field(default="jpeg", description="TurboJPEG格式提示(jpeg/png/auto)")

    # V5.2新增：CSS渲染稳定性配置
    css_stability: CSSStabilityConfig = Field(default_factory=CSSStabilityConfig, description="CSS渲染稳定性配置")

    def resolve_max_workers(self) -> int:
        """
        解析实际的工作线程数

        Returns:
            实际的工作线程数
        """
        if isinstance(self.max_workers, str) and self.max_workers.lower() == "auto":
            # 自动检测CPU核心数
            return max(1, os.cpu_count() or 1)
        elif isinstance(self.max_workers, int):
            return max(1, min(16, self.max_workers))  # 限制在1-16之间
        else:
            # 如果是其他字符串，尝试转换为整数
            try:
                workers = int(self.max_workers)
                return max(1, min(16, workers))
            except (ValueError, TypeError):
                # 转换失败，使用默认值
                return max(1, os.cpu_count() or 1)

    @field_validator('max_workers')
    @classmethod
    def validate_max_workers(cls, v):
        """验证max_workers字段"""
        if isinstance(v, str):
            if v.lower() == "auto":
                return v
            else:
                # 尝试转换为整数
                try:
                    workers = int(v)
                    if workers < 1 or workers > 16:
                        raise ValueError("工作线程数必须在1-16之间")
                    return workers
                except ValueError:
                    raise ValueError("max_workers必须是'auto'或1-16之间的整数")
        elif isinstance(v, int):
            if v < 1 or v > 16:
                raise ValueError("工作线程数必须在1-16之间")
            return v
        else:
            raise ValueError("max_workers必须是字符串'auto'或整数")

    @field_validator('turbo_jpeg_format')
    @classmethod
    def validate_turbo_jpeg_format(cls, v):
        """验证TurboJPEG格式参数"""
        valid_formats = ["jpeg", "png", "auto"]
        if v.lower() not in valid_formats:
            raise ValueError(f"turbo_jpeg_format必须是{valid_formats}中的一个")
        return v.lower()


class DegradationEffectConfig(BaseModel):
    """单个降质效果配置"""
    probability: float = Field(default=0.2, ge=0.0, le=1.0, description="降质效果触发概率")


class PostprocessingConfig(BaseModel):
    """图像后处理配置"""
    perspective: Optional[PerspectiveConfig] = Field(default=None, description="透视变换效果配置")
    background: Optional[BackgroundConfig] = Field(default=None, description="背景图合成配置")
    # V4.3新增：表格融合配置
    table_blending: Optional[TableBlendingConfig] = Field(default=None, description="表格融合配置")

    # V4.5新增：降质效果配置
    degradation_blur: Optional[DegradationEffectConfig] = Field(default=None, description="模糊效果（高斯/运动/均值模糊随机选择）")
    degradation_noise: Optional[DegradationEffectConfig] = Field(default=None, description="高斯噪声")
    degradation_fade_global: Optional[DegradationEffectConfig] = Field(default=None, description="全局褪色")
    degradation_fade_local: Optional[DegradationEffectConfig] = Field(default=None, description="局部褪色")
    degradation_uneven_lighting: Optional[DegradationEffectConfig] = Field(default=None, description="不均匀光照")
    degradation_jpeg: Optional[DegradationEffectConfig] = Field(default=None, description="JPEG压缩")
    degradation_darker_brighter: Optional[DegradationEffectConfig] = Field(default=None, description="亮度/对比度调整")
    degradation_gamma_correction: Optional[DegradationEffectConfig] = Field(default=None, description="伽马校正")


class RenderConfig(BaseModel):
    """主配置模型"""
    output: OutputConfig = Field(default_factory=OutputConfig)
    structure: StructureConfig
    content: ContentConfig = Field(default_factory=ContentConfig)
    style: StyleConfig = Field(default_factory=StyleConfig)
    seed: int = Field(default=42, description="随机种子，用于可复现性")

    # V4.0新增：图像后处理配置
    postprocessing: Optional[PostprocessingConfig] = Field(default=None, description="图像后处理配置")

    # V4.4.2新增：调试配置
    debug: Optional[DebugConfig] = Field(default=None, description="调试配置")

    # V5.2新增：性能优化配置
    performance: Optional[PerformanceConfig] = Field(default=None, description="性能优化配置")

    class Config:
        """Pydantic配置"""
        extra = "forbid"  # 禁止额外字段





# ============================================================================
# V3.1 新增：ResolvedParams 模型
# 这些模型包含解析后的具体参数，用于替代原始配置传递给Builder
# ============================================================================

class ResolvedOutputParams(BaseModel):
    """解析后的输出参数"""
    output_dir: str
    label_suffix: Optional[str] = None


class ResolvedStructureParams(BaseModel):
    """解析后的结构参数 - 所有值都是具体的、确定性的"""
    body_rows: int
    cols: int
    header_rows: int
    merge_probability: float
    max_row_span: int
    max_col_span: int
    complex_header: Optional[ComplexHeaderConfig]


class ResolvedContentParams(BaseModel):
    """解析后的内容参数"""
    source_type: Literal["programmatic", "csv"]

    # CSV相关参数
    csv_file_path: Optional[str]  # 最终选择的CSV文件路径
    csv_encoding: Optional[str]
    csv_mismatch_strategy: Optional[str]

    # V5.0新增：CSV目录和采样配置
    csv_dirs: Optional[List[str]]
    csv_dir_probabilities: Optional[List[float]]
    sampling_mode: Optional[str]
    blank_control: Optional[Dict[str, float]]

    # 程序化生成参数
    programmatic_types: Optional[List[str]]


class ResolvedFontParams(BaseModel):
    """解析后的字体参数 - 所有值都是具体的、确定性的"""
    font_dirs: List[str]  # V3.2：支持多个字体目录
    default_family: str  # 从列表中选择的具体字体
    default_size: int    # 从范围中选择的具体大小
    bold_probability: float
    italic_probability: float
    fallback_font: str   # V3.2：回退字体


class ResolvedSizingParams(BaseModel):
    """解析后的sizing参数 - 包含每行每列的具体尺寸"""
    # 每行的具体高度 {row_index: height_value}
    row_heights: Dict[int, Union[int, str]] = Field(default_factory=dict, description="每行的具体高度")
    # 每列的具体宽度 {col_index: width_value}
    col_widths: Dict[int, Union[int, str]] = Field(default_factory=dict, description="每列的具体宽度")
    # 默认值
    default_row_height: Union[int, str] = Field(default='auto', description="默认行高")
    default_col_width: Union[int, str] = Field(default='auto', description="默认列宽")


class ResolvedBaseStyleParams(BaseModel):
    """解析后的基础样式参数 - 所有值都是具体的、确定性的"""
    font: ResolvedFontParams
    text_color: str      # 从列表中选择的具体颜色
    background_color: str # 从列表中选择的具体颜色
    horizontal_align: Literal['left', 'center', 'right']  # 从列表中选择的具体对齐方式
    vertical_align: Literal['top', 'middle', 'bottom']    # 从列表中选择的具体对齐方式
    padding: int         # 从范围中选择的具体值


class ResolvedStyleParams(BaseModel):
    """解析后的样式参数"""
    header: ResolvedBaseStyleParams
    body: ResolvedBaseStyleParams
    zebra_stripes: bool
    zebra_colors: Optional[List[str]] = None  # 斑马纹颜色：[奇数行背景色, 偶数行背景色]
    sizing: ResolvedSizingParams  # V3.4：使用解析后的sizing参数
    hierarchical: Optional[HierarchicalStyleConfig]

    # V3.2新增：边框模式解析结果
    border_mode: str  # 'full', 'none', 'semi'
    border_details: Optional[Dict[str, Any]] = None  # 边框详细配置

    # 新增：合并单元格对齐优化配置
    merged_cell_center_probability: float = Field(default=0.0, description="合并单元格相对居中对齐的概率")

    # V4.5新增：边框颜色随机化概率
    randomize_border_color_probability: float = Field(default=0.0, ge=0.0, le=1.0, description="线条颜色随机化概率")

    # V4.5新增：颜色对比度配置
    color_contrast: ColorContrastConfig = Field(default_factory=ColorContrastConfig, description="颜色对比度配置")

    # V4.0新增：内容溢出处理策略
    overflow_strategy: Literal['truncate', 'wrap'] = Field(default='wrap', description="内容溢出处理策略")


class ResolvedPerformanceParams(BaseModel):
    """解析后的性能参数"""
    enable_parallel: bool = Field(default=False, description="是否启用并行处理")
    max_workers: int = Field(default=1, description="最大工作线程数")
    enable_turbo_jpeg: bool = Field(default=True, description="是否启用TurboJPEG优化")
    turbo_jpeg_quality: int = Field(default=95, description="JPEG质量参数")
    turbo_jpeg_format: str = Field(default="jpeg", description="TurboJPEG格式")
    css_stability_enabled: bool = Field(default=False, description="CSS稳定性检查启用状态")


class ResolvedPostprocessingParams(BaseModel):
    """解析后的图像后处理参数"""
    apply_perspective: bool = Field(default=False, description="是否应用透视变换")
    perspective_offset_ratio: Optional[float] = Field(default=None, description="透视变换偏移比例")
    content_area_shrink_ratio: Optional[float] = Field(default=0.1, description="内容区域缩小比例")
    apply_background: bool = Field(default=False, description="是否应用背景图合成")
    background_image_path: Optional[str] = Field(default=None, description="选中的背景图路径")
    max_scale_factor: Optional[float] = Field(default=None, description="最大缩放倍数（V5.1已弃用，现在使用智能动态适应）")
    # CSS渲染模式专用参数
    css_background_width: Optional[int] = Field(default=None, description="CSS背景图宽度")
    css_background_height: Optional[int] = Field(default=None, description="CSS背景图高度")
    css_table_left: Optional[int] = Field(default=None, description="表格在背景中的左偏移")
    css_table_top: Optional[int] = Field(default=None, description="表格在背景中的顶偏移")
    css_crop_width: Optional[int] = Field(default=None, description="最终裁剪宽度")
    css_crop_height: Optional[int] = Field(default=None, description="最终裁剪高度")
    css_bg_offset_x: Optional[int] = Field(default=None, description="背景图随机裁剪X偏移")
    css_bg_offset_y: Optional[int] = Field(default=None, description="背景图随机裁剪Y偏移")
    # V4.2新增：边距控制配置
    margin_control: Optional[MarginControlConfig] = Field(default=None, description="边距控制配置")
    # V5.2新增：背景处理模式标识
    css_background_applied: bool = Field(default=False, description="CSS阶段是否已应用背景图")
    # V4.3新增：表格透明度参数
    enable_transparency: bool = Field(default=False, description="是否启用表格透明度")
    default_color_transparency: float = Field(default=0.1, description="默认色（白色/浅色）的透明度")
    meaningful_color_transparency: float = Field(default=0.7, description="有意义颜色的透明度")

    # V4.5新增：降质效果解析后参数
    apply_degradation_blur: bool = Field(default=False, description="是否应用降质模糊效果")
    apply_degradation_noise: bool = Field(default=False, description="是否应用降质噪声效果")
    apply_degradation_fade_global: bool = Field(default=False, description="是否应用全局褪色效果")
    apply_degradation_fade_local: bool = Field(default=False, description="是否应用局部褪色效果")
    apply_degradation_uneven_lighting: bool = Field(default=False, description="是否应用不均匀光照效果")
    apply_degradation_jpeg: bool = Field(default=False, description="是否应用JPEG压缩效果")
    apply_degradation_darker_brighter: bool = Field(default=False, description="是否应用亮度/对比度调整效果")
    apply_degradation_gamma_correction: bool = Field(default=False, description="是否应用伽马校正效果")


class ResolvedParams(BaseModel):
    """解析后的完整参数"""
    structure: ResolvedStructureParams
    content: ResolvedContentParams
    style: ResolvedStyleParams
    output: ResolvedOutputParams
    seed: int

    # V4.0新增：图像后处理参数
    postprocessing: Optional[ResolvedPostprocessingParams] = Field(default=None, description="解析后的图像后处理参数")

    # V6.0新增：性能参数
    performance: Optional[ResolvedPerformanceParams] = Field(default=None, description="解析后的性能参数")

    # V5.1调试：透视变换adaptive_scaling调试信息
    perspective_adaptive_debug: Optional[dict] = Field(default=None, description="透视变换动态缩放调试信息")

    class Config:
        """Pydantic配置"""
        extra = "forbid"
