"""
透明度处理工具类

V4.3新增：提供颜色格式转换、文字可读性优化等透明度相关功能。
"""

import re
import logging
from typing import Tuple, Optional


class TransparencyUtils:
    """
    透明度处理工具类
    
    提供颜色格式转换、文字可读性优化等功能。
    """
    
    def __init__(self):
        """初始化透明度工具"""
        self.logger = logging.getLogger(__name__)
    
    def hex_to_rgba(self, hex_color: str, alpha: float) -> str:
        """
        将十六进制颜色转换为rgba格式
        
        Args:
            hex_color: 十六进制颜色值，如 "#FFFFFF" 或 "#FFF"
            alpha: 透明度值，范围 0.0-1.0
            
        Returns:
            rgba格式的颜色字符串，如 "rgba(255, 255, 255, 0.8)"
        """
        # 移除 # 前缀
        hex_color = hex_color.lstrip('#')
        
        # 处理3位和6位十六进制颜色
        if len(hex_color) == 3:
            hex_color = ''.join([c*2 for c in hex_color])
        elif len(hex_color) != 6:
            raise ValueError(f"无效的十六进制颜色格式: #{hex_color}")
        
        # 转换为RGB值
        try:
            r = int(hex_color[0:2], 16)
            g = int(hex_color[2:4], 16)
            b = int(hex_color[4:6], 16)
        except ValueError:
            raise ValueError(f"无效的十六进制颜色值: #{hex_color}")
        
        # 确保alpha值在有效范围内
        alpha = max(0.0, min(1.0, alpha))
        
        return f"rgba({r}, {g}, {b}, {alpha})"
    
    def rgb_to_rgba(self, rgb_color: str, alpha: float) -> str:
        """
        将rgb颜色转换为rgba格式
        
        Args:
            rgb_color: rgb格式的颜色值，如 "rgb(255, 255, 255)"
            alpha: 透明度值，范围 0.0-1.0
            
        Returns:
            rgba格式的颜色字符串
        """
        # 使用正则表达式提取RGB值
        rgb_pattern = r'rgb\s*\(\s*(\d+)\s*,\s*(\d+)\s*,\s*(\d+)\s*\)'
        match = re.match(rgb_pattern, rgb_color.strip())
        
        if not match:
            raise ValueError(f"无效的RGB颜色格式: {rgb_color}")
        
        r, g, b = map(int, match.groups())
        
        # 确保RGB值在有效范围内
        r = max(0, min(255, r))
        g = max(0, min(255, g))
        b = max(0, min(255, b))
        
        # 确保alpha值在有效范围内
        alpha = max(0.0, min(1.0, alpha))
        
        return f"rgba({r}, {g}, {b}, {alpha})"
    
    def color_to_rgba(self, color: str, alpha: float) -> str:
        """
        将任意格式的颜色转换为rgba格式
        
        Args:
            color: 颜色值，支持十六进制、rgb格式或颜色名称
            alpha: 透明度值，范围 0.0-1.0
            
        Returns:
            rgba格式的颜色字符串
        """
        color = color.strip()
        
        # 处理十六进制颜色
        if color.startswith('#'):
            return self.hex_to_rgba(color, alpha)
        
        # 处理rgb颜色
        if color.startswith('rgb('):
            return self.rgb_to_rgba(color, alpha)
        
        # 处理已经是rgba格式的颜色
        if color.startswith('rgba('):
            # 提取现有的RGB值，替换alpha值
            rgba_pattern = r'rgba\s*\(\s*(\d+)\s*,\s*(\d+)\s*,\s*(\d+)\s*,\s*[\d.]+\s*\)'
            match = re.match(rgba_pattern, color)
            if match:
                r, g, b = map(int, match.groups())
                alpha = max(0.0, min(1.0, alpha))
                return f"rgba({r}, {g}, {b}, {alpha})"
        
        # 处理颜色名称（转换为十六进制后处理）
        color_names = {
            'white': '#FFFFFF',
            'black': '#000000',
            'red': '#FF0000',
            'green': '#008000',
            'blue': '#0000FF',
            'yellow': '#FFFF00',
            'cyan': '#00FFFF',
            'magenta': '#FF00FF',
            'gray': '#808080',
            'grey': '#808080',
            'transparent': 'rgba(0, 0, 0, 0)'
        }
        
        color_lower = color.lower()
        if color_lower in color_names:
            if color_lower == 'transparent':
                return color_names[color_lower]
            return self.hex_to_rgba(color_names[color_lower], alpha)
        
        # 如果无法识别格式，记录警告并返回默认值
        self.logger.warning(f"无法识别的颜色格式: {color}，使用默认白色")
        return self.hex_to_rgba('#FFFFFF', alpha)
    
    def calculate_luminance(self, color: str) -> float:
        """
        计算颜色的亮度值（用于对比度计算）
        
        Args:
            color: 颜色值，支持多种格式
            
        Returns:
            亮度值，范围 0.0-1.0
        """
        # 先转换为rgba格式以提取RGB值
        rgba_color = self.color_to_rgba(color, 1.0)
        
        # 提取RGB值
        rgba_pattern = r'rgba\s*\(\s*(\d+)\s*,\s*(\d+)\s*,\s*(\d+)\s*,\s*[\d.]+\s*\)'
        match = re.match(rgba_pattern, rgba_color)
        
        if not match:
            self.logger.warning(f"无法计算颜色亮度: {color}")
            return 0.5  # 返回中等亮度
        
        r, g, b = map(int, match.groups())
        
        # 使用相对亮度公式（WCAG标准）
        def gamma_correct(c):
            c = c / 255.0
            if c <= 0.03928:
                return c / 12.92
            else:
                return pow((c + 0.055) / 1.055, 2.4)
        
        r_linear = gamma_correct(r)
        g_linear = gamma_correct(g)
        b_linear = gamma_correct(b)
        
        # 计算相对亮度
        luminance = 0.2126 * r_linear + 0.7152 * g_linear + 0.0722 * b_linear
        return luminance
    
    def calculate_contrast_ratio(self, color1: str, color2: str) -> float:
        """
        计算两个颜色之间的对比度比例
        
        Args:
            color1: 第一个颜色
            color2: 第二个颜色
            
        Returns:
            对比度比例，范围 1.0-21.0
        """
        lum1 = self.calculate_luminance(color1)
        lum2 = self.calculate_luminance(color2)
        
        # 确保较亮的颜色在分子位置
        lighter = max(lum1, lum2)
        darker = min(lum1, lum2)
        
        # 计算对比度比例
        contrast_ratio = (lighter + 0.05) / (darker + 0.05)
        return contrast_ratio
    
    def optimize_text_color_for_background(self, text_color: str, background_color: str, 
                                         min_contrast_ratio: float = 4.5) -> str:
        """
        根据背景颜色优化文字颜色以确保可读性
        
        Args:
            text_color: 原始文字颜色
            background_color: 背景颜色
            min_contrast_ratio: 最小对比度要求
            
        Returns:
            优化后的文字颜色
        """
        current_contrast = self.calculate_contrast_ratio(text_color, background_color)
        
        # 如果当前对比度已经足够，直接返回
        if current_contrast >= min_contrast_ratio:
            return text_color
        
        # 计算背景亮度
        bg_luminance = self.calculate_luminance(background_color)
        
        # 根据背景亮度选择更合适的文字颜色
        if bg_luminance > 0.5:
            # 背景较亮，使用深色文字
            optimized_color = '#000000'
        else:
            # 背景较暗，使用浅色文字
            optimized_color = '#FFFFFF'
        
        # 验证优化后的对比度
        optimized_contrast = self.calculate_contrast_ratio(optimized_color, background_color)
        
        if optimized_contrast >= min_contrast_ratio:
            self.logger.debug(f"文字颜色已优化: {text_color} -> {optimized_color} "
                            f"(对比度: {current_contrast:.2f} -> {optimized_contrast:.2f})")
            return optimized_color
        else:
            # 如果仍然不够，记录警告但返回优化后的颜色
            self.logger.warning(f"无法达到最小对比度要求 {min_contrast_ratio}，"
                              f"当前对比度: {optimized_contrast:.2f}")
            return optimized_color
    
    def generate_text_shadow_css(self, text_color: str, background_color: str) -> str:
        """
        根据文字和背景颜色生成合适的文字阴影CSS
        
        Args:
            text_color: 文字颜色
            background_color: 背景颜色
            
        Returns:
            CSS text-shadow 属性值
        """
        # 计算背景亮度
        bg_luminance = self.calculate_luminance(background_color)
        text_luminance = self.calculate_luminance(text_color)
        
        # 根据亮度差异决定阴影颜色和强度
        if abs(bg_luminance - text_luminance) < 0.3:
            # 亮度差异较小，需要更明显的阴影
            if bg_luminance > 0.5:
                # 背景较亮，使用深色阴影
                shadow_color = "rgba(0, 0, 0, 0.8)"
                shadow_blur = "2px"
            else:
                # 背景较暗，使用浅色阴影
                shadow_color = "rgba(255, 255, 255, 0.8)"
                shadow_blur = "2px"
        else:
            # 亮度差异较大，使用轻微阴影
            if text_luminance > bg_luminance:
                # 文字比背景亮，使用深色阴影
                shadow_color = "rgba(0, 0, 0, 0.3)"
                shadow_blur = "1px"
            else:
                # 文字比背景暗，使用浅色阴影
                shadow_color = "rgba(255, 255, 255, 0.3)"
                shadow_blur = "1px"
        
        return f"1px 1px {shadow_blur} {shadow_color}"
    
    def validate_transparency_level(self, transparency: float) -> float:
        """
        验证并规范化透明度级别

        Args:
            transparency: 透明度值

        Returns:
            规范化后的透明度值（0.0-1.0）
        """
        if transparency < 0.0:
            self.logger.warning(f"透明度值 {transparency} 小于0，已调整为0.0")
            return 0.0
        elif transparency > 1.0:
            self.logger.warning(f"透明度值 {transparency} 大于1，已调整为1.0")
            return 1.0
        else:
            return transparency

    def _extract_rgb_values(self, color: str) -> Tuple[int, int, int]:
        """
        从颜色字符串中提取RGB值

        Args:
            color: 颜色值，支持多种格式

        Returns:
            RGB值元组 (r, g, b)
        """
        # 先转换为rgba格式以提取RGB值
        rgba_color = self.color_to_rgba(color, 1.0)

        # 提取RGB值
        rgba_pattern = r'rgba\s*\(\s*(\d+)\s*,\s*(\d+)\s*,\s*(\d+)\s*,\s*[\d.]+\s*\)'
        match = re.match(rgba_pattern, rgba_color)

        if not match:
            self.logger.warning(f"无法提取RGB值: {color}，使用默认白色")
            return (255, 255, 255)  # 默认白色

        r, g, b = map(int, match.groups())
        return (r, g, b)

    def is_default_color(self, color: str) -> bool:
        """
        判断是否为默认色（白色/浅色系）

        Args:
            color: 颜色值，支持多种格式

        Returns:
            True表示默认色，False表示有意义颜色
        """
        try:
            r, g, b = self._extract_rgb_values(color)

            # 白色系：RGB都大于240
            if r >= 240 and g >= 240 and b >= 240:
                return True

            # 浅灰色系：RGB差异小且都较大
            # if abs(r - g) <= 15 and abs(g - b) <= 15 and r >= 230:
            #     return True

            return False
        except Exception as e:
            self.logger.warning(f"判断颜色类型时出错: {color}, {e}，默认为有意义颜色")
            return False  # 出错时默认为有意义颜色，保持颜色可见性

    def get_appropriate_transparency(self, color: str, transparency_config) -> float:
        """
        根据颜色类型返回合适的透明度值

        Args:
            color: 颜色值
            transparency_config: 透明度配置对象

        Returns:
            适合该颜色的透明度值
        """
        if self.is_default_color(color):
            transparency = transparency_config.default_color_transparency
            self.logger.debug(f"颜色 {color} 被识别为默认色，使用透明度: {transparency}")
            return transparency
        else:
            transparency = transparency_config.meaningful_color_transparency
            self.logger.debug(f"颜色 {color} 被识别为有意义颜色，使用透明度: {transparency}")
            return transparency
