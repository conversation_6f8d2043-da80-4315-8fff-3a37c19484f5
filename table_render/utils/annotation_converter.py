"""
标注转换器

将原始的浏览器标注数据转换为符合PRD规范的标注格式。
"""

import logging
from typing import Dict, List, Any

from ..models import TableModel


class AnnotationConverter:
    """
    标注转换器类
    
    负责将浏览器获取的原始标注数据转换为最终的JSON格式。
    """
    
    def __init__(self):
        """初始化标注转换器"""
        self.logger = logging.getLogger(__name__)
    
    def convert_to_final_format(
        self,
        raw_annotations: Dict[str, Any],
        table_model: TableModel,
        image_filename: str
    ) -> Dict[str, Any]:
        """
        将原始标注转换为最终格式
        
        Args:
            raw_annotations: 从浏览器获取的原始标注数据
            table_model: 表格模型，用于获取逻辑位置信息
            image_filename: 图像文件名
            
        Returns:
            符合PRD规范的标注字典
        """
        self.logger.debug("开始转换标注格式")
        
        # 创建单元格ID到模型的映射
        cell_id_to_model = self._create_cell_mapping(table_model)

        # 根据边框模式确定type值：full模式为1，其他模式为2
        annotation_type = 1 if getattr(table_model, 'border_mode', None) == 'full' else 2

        # 构建最终标注格式
        final_annotations = {
            "table_ind": image_filename.replace('.png', ''),
            "image_path": image_filename,
            "type": annotation_type,
            "cells": []
        }
        
        # 转换每个单元格的标注
        for i, cell_data in enumerate(raw_annotations.get('cells', [])):
            cell_id = cell_data['id']
            cell_model = cell_id_to_model.get(cell_id)

            if cell_model is None:
                self.logger.warning(f"未找到单元格模型: {cell_id}")
                continue

            # 转换bbox格式：从[x_min, y_min, x_max, y_max]到四个角点
            bbox_rect = cell_data['bbox']
            self.logger.debug(f"转换单元格{i} ({cell_id}) bbox: {bbox_rect}")
            bbox_corners = self._convert_bbox_to_corners(bbox_rect)
            self.logger.debug(f"转换后的四角点: {bbox_corners}")
            
            # V4.5修正：结合原始边框状态和颜色差异映射生成最终边框标注
            final_border_style = self._get_final_border_style(cell_model, table_model)

            # 构建单元格标注
            cell_annotation = {
                "cell_ind": i,
                "header": cell_model.is_header,
                "content": self._create_content_annotation(cell_data['content']),
                "bbox": bbox_corners,
                "border": {
                    "style": {
                        "top": final_border_style['top'],
                        "right": final_border_style['right'],
                        "bottom": final_border_style['bottom'],
                        "left": final_border_style['left']
                    }
                },
                "lloc": {
                    "start_row": cell_model.row_index,
                    "end_row": cell_model.row_index + cell_model.row_span - 1,
                    "start_col": cell_model.col_index,
                    "end_col": cell_model.col_index + cell_model.col_span - 1
                }
            }
            
            final_annotations["cells"].append(cell_annotation)
        
        self.logger.debug(f"标注转换完成，共{len(final_annotations['cells'])}个单元格")
        return final_annotations
    
    def _create_cell_mapping(self, table_model: TableModel) -> Dict[str, Any]:
        """
        创建单元格ID到模型的映射
        
        Args:
            table_model: 表格模型
            
        Returns:
            单元格ID到CellModel的映射字典
        """
        mapping = {}
        for row in table_model.rows:
            for cell in row.cells:
                mapping[cell.cell_id] = cell
        return mapping

    def _get_final_border_style(self, cell_model, table_model) -> Dict[str, int]:
        """
        V4.5新增：获取最终的边框样式，结合原始边框状态和颜色差异映射
        智能表头分割线：对表头分割线位置使用颜色差异检测结果

        Args:
            cell_model: 单元格模型
            table_model: 表格模型

        Returns:
            最终的边框样式字典 {'top': int, 'right': int, 'bottom': int, 'left': int}
        """
        # 获取原始边框状态
        original_border = {
            'top': cell_model.border_style.top,
            'right': cell_model.border_style.right,
            'bottom': cell_model.border_style.bottom,
            'left': cell_model.border_style.left
        }

        # 如果没有颜色差异映射，直接返回原始边框状态
        if not hasattr(table_model, 'color_diff_borders') or table_model.color_diff_borders is None:
            return original_border

        # 获取该单元格的颜色差异边框信息
        color_diff_info = table_model.color_diff_borders.get(cell_model.cell_id, {})

        # 获取表头分割线位置信息，确保不为None
        header_separator_positions = getattr(table_model, 'header_separator_positions', None)
        if header_separator_positions is None:
            header_separator_positions = set()

        # 计算最终边框状态并记录调整信息
        final_border = {}
        border_adjustments = []

        for direction in ['top', 'right', 'bottom', 'left']:
            original_has_border = original_border.get(direction, 0)
            color_diff_has_border = color_diff_info.get(direction, False)

            # 检查是否是表头分割线位置
            is_header_separator = (cell_model.row_index, cell_model.col_index, direction) in header_separator_positions

            if is_header_separator:
                # 对于表头分割线位置，优先使用颜色差异检测结果
                final_border[direction] = 1 if color_diff_has_border else 0
                if color_diff_has_border != bool(original_has_border):
                    self.logger.debug(f"表头分割线 ({cell_model.row_index}, {cell_model.col_index}, {direction}) "
                                    f"智能调整: 原始={original_has_border} -> 最终={final_border[direction]}")
                    # 记录表头分割线调整信息
                    adjustment = {
                        "cell_id": cell_model.cell_id,
                        "cell_position": (cell_model.row_index, cell_model.col_index),
                        "direction": direction,
                        "original_border": original_has_border,
                        "final_border": final_border[direction],
                        "adjustment_type": "header_separator_color_diff",
                        "color_diff_triggered": color_diff_has_border
                    }
                    border_adjustments.append(adjustment)
            else:
                # 非表头分割线位置，使用OR逻辑：原始边框 OR 颜色差异边框
                final_border[direction] = 1 if (original_has_border or color_diff_has_border) else 0
                if color_diff_has_border and not original_has_border:
                    # 记录OR逻辑触发的调整
                    adjustment = {
                        "cell_id": cell_model.cell_id,
                        "cell_position": (cell_model.row_index, cell_model.col_index),
                        "direction": direction,
                        "original_border": original_has_border,
                        "final_border": final_border[direction],
                        "adjustment_type": "color_diff_or_logic",
                        "color_diff_triggered": color_diff_has_border
                    }
                    border_adjustments.append(adjustment)

        # 记录边框调整信息到metadata
        if border_adjustments:
            from .metadata_collector import metadata_collector
            existing_adjustments = metadata_collector.get_value("border_adjustments", [])
            existing_adjustments.extend(border_adjustments)
            metadata_collector.record_intermediate_value("border_adjustments", existing_adjustments)

        return final_border
    
    def _convert_bbox_to_corners(self, bbox_rect: List[float]) -> Dict[str, List[float]]:
        """
        将矩形bbox转换为四个角点格式
        
        Args:
            bbox_rect: [x_min, y_min, x_max, y_max]格式的边界框
            
        Returns:
            包含p1-p4四个角点的字典
        """
        x_min, y_min, x_max, y_max = bbox_rect
        return {
            "p1": [x_min, y_min],      # 左上角
            "p2": [x_max, y_min],      # 右上角
            "p3": [x_max, y_max],      # 右下角
            "p4": [x_min, y_max]       # 左下角
        }
    
    def _create_content_annotation(self, content_text: str) -> List[Dict[str, Any]]:
        """
        创建内容标注
        
        Args:
            content_text: 单元格的文本内容
            
        Returns:
            内容标注列表（V1版本简化处理）
        """
        if not content_text.strip():
            return []
        
        # V1版本简化处理：将整个内容作为一个文本块
        # 在实际应用中，这里应该进行更精细的文本分割和定位
        return [
            {
                "bbox": {
                    "p1": [0, 0],
                    "p2": [0, 0], 
                    "p3": [0, 0],
                    "p4": [0, 0]
                },
                "direction": 0,
                "text": content_text,
                "score": 1.0
            }
        ]
