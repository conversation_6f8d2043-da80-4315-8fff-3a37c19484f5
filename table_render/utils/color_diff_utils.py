"""
颜色差异工具

V4.5新增：专门处理相邻单元格颜色差异检测和边框标注调整的工具类。
"""

import logging
from typing import Dict, Tuple, List, Optional

from .color_utils import ColorManager


class ColorDifferenceDetector:
    """
    颜色差异检测器
    
    负责检测相邻单元格的颜色差异，并根据差异调整边框标注。
    """
    
    def __init__(self, color_manager: ColorManager):
        """
        初始化颜色差异检测器
        
        Args:
            color_manager: 颜色管理器实例
        """
        self.color_manager = color_manager
        self.logger = logging.getLogger(__name__)
        
        # 用于记录边缘单元格的默认色判断信息
        self.boundary_cells_default_color_info = {}

    def detect_color_based_borders_with_actual_colors(self, table_model, actual_colors):
        """
        V4.5修正：使用实际颜色进行颜色差异边框检测，生成独立的差异映射
        智能表头分割线：对表头分割线位置进行特殊处理

        Args:
            table_model: 表格模型
            actual_colors: 实际的单元格颜色映射 {cell_id: background_color}

        Returns:
            颜色差异边框映射 {cell_id: {'top': bool, 'right': bool, 'bottom': bool, 'left': bool}}
        """
        self.logger.debug("开始基于实际颜色检测颜色差异边框")
        
        # 清空边缘单元格默认色信息
        self.boundary_cells_default_color_info = {}

        # 创建单元格位置映射，便于查找相邻单元格
        cell_map = self._create_cell_position_map(table_model)

        # 颜色差异边框映射
        color_diff_borders = {}

        # 新增：初始化颜色差异metadata收集器
        color_diff_records = []
        statistics = {
            "total_borders_checked": 0,
            "color_diff_triggered": 0,
            "header_separator_affected": 0,
            "max_color_difference": 0,
            "min_color_difference": 999
        }

        # 为每个单元格检测颜色差异
        for row in table_model.rows:
            for cell in row.cells:
                color_diff_borders[cell.cell_id] = self._detect_cell_color_borders_with_actual_colors(
                    cell, cell_map, actual_colors, table_model, color_diff_records, statistics
                )

        # 修正统计信息中的最小值（如果没有触发任何颜色差异）
        if statistics["color_diff_triggered"] == 0:
            statistics["min_color_difference"] = 0

        # 记录颜色差异信息到metadata
        from .metadata_collector import metadata_collector
        metadata_collector.record_multiple_values({
            "color_diff_triggered_borders": color_diff_records,
            "color_diff_statistics": statistics,
            "boundary_cells_default_color_info": self.boundary_cells_default_color_info
        })

        self.logger.debug(f"基于实际颜色的颜色差异边框检测完成，触发{statistics['color_diff_triggered']}个边框")
        self.logger.debug(f"边缘单元格默认色信息已记录：{len(self.boundary_cells_default_color_info)}个边缘单元格")
        return color_diff_borders

    def _detect_cell_color_borders_with_actual_colors(self, cell, cell_map, actual_colors, table_model,
                                                     color_diff_records=None, statistics=None):
        """
        V4.5修正：使用实际颜色检测单个单元格的颜色差异边框，返回差异结果
        智能表头分割线：对表头分割线位置进行特殊的颜色差异检测

        Args:
            cell: 单元格对象
            cell_map: 单元格位置映射
            actual_colors: 实际的单元格颜色映射
            table_model: 表格模型（用于获取表头分割线位置信息）
            color_diff_records: 颜色差异记录列表（用于收集metadata）
            statistics: 统计信息字典（用于收集metadata）

        Returns:
            颜色差异边框字典 {'top': bool, 'right': bool, 'bottom': bool, 'left': bool}
        """
        # 获取单元格的实际背景色
        cell_bg_color = actual_colors.get(cell.cell_id, '#FFFFFF')

        # 初始化颜色差异边框结果
        color_diff_result = {
            'top': False,
            'right': False,
            'bottom': False,
            'left': False
        }

        # 获取表头分割线位置信息，确保不为None
        header_separator_positions = getattr(table_model, 'header_separator_positions', None)
        if header_separator_positions is None:
            header_separator_positions = set()

        # 检查上边框
        adjacent_cell = self._get_adjacent_cell(cell, cell_map, 'top')
        if adjacent_cell:
            adjacent_bg_color = actual_colors.get(adjacent_cell.cell_id, '#FFFFFF')
            color_difference = self.color_manager.calculate_rgb_difference(cell_bg_color, adjacent_bg_color)

            # 更新统计信息
            if statistics:
                statistics["total_borders_checked"] += 1
                statistics["max_color_difference"] = max(statistics["max_color_difference"], color_difference)

            if self._should_force_border(cell_bg_color, adjacent_bg_color):
                color_diff_result['top'] = True
                self._record_color_diff_border(cell, adjacent_cell, 'top', cell_bg_color, adjacent_bg_color,
                                             color_difference, False, color_diff_records, statistics)

        # 检查右边框
        adjacent_cell = self._get_adjacent_cell(cell, cell_map, 'right')
        if adjacent_cell:
            adjacent_bg_color = actual_colors.get(adjacent_cell.cell_id, '#FFFFFF')
            color_difference = self.color_manager.calculate_rgb_difference(cell_bg_color, adjacent_bg_color)

            # 更新统计信息
            if statistics:
                statistics["total_borders_checked"] += 1
                statistics["max_color_difference"] = max(statistics["max_color_difference"], color_difference)

            if self._should_force_border(cell_bg_color, adjacent_bg_color):
                color_diff_result['right'] = True
                self._record_color_diff_border(cell, adjacent_cell, 'right', cell_bg_color, adjacent_bg_color,
                                             color_difference, False, color_diff_records, statistics)

        # 检查下边框（智能表头分割线处理）
        adjacent_cell = self._get_adjacent_cell(cell, cell_map, 'bottom')
        if adjacent_cell:
            adjacent_bg_color = actual_colors.get(adjacent_cell.cell_id, '#FFFFFF')
            color_difference = self.color_manager.calculate_rgb_difference(cell_bg_color, adjacent_bg_color)

            # 更新统计信息
            if statistics:
                statistics["total_borders_checked"] += 1
                statistics["max_color_difference"] = max(statistics["max_color_difference"], color_difference)

            # 检查是否是表头分割线位置
            is_header_separator = (cell.row_index, cell.col_index, 'bottom') in header_separator_positions

            if is_header_separator:
                # 对于表头分割线，基于颜色差异智能决定是否显示
                if self._should_force_border(cell_bg_color, adjacent_bg_color):
                    color_diff_result['bottom'] = True
                    self.logger.debug(f"表头分割线位置 ({cell.row_index}, {cell.col_index}) 颜色差异显著，显示边框")
                    self._record_color_diff_border(cell, adjacent_cell, 'bottom', cell_bg_color, adjacent_bg_color,
                                                 color_difference, True, color_diff_records, statistics)
                else:
                    color_diff_result['bottom'] = False
                    self.logger.debug(f"表头分割线位置 ({cell.row_index}, {cell.col_index}) 颜色相似，不显示边框")
            else:
                # 非表头分割线位置，按正常逻辑处理
                if self._should_force_border(cell_bg_color, adjacent_bg_color):
                    color_diff_result['bottom'] = True
                    self._record_color_diff_border(cell, adjacent_cell, 'bottom', cell_bg_color, adjacent_bg_color,
                                                 color_difference, False, color_diff_records, statistics)

        # 检查左边框
        adjacent_cell = self._get_adjacent_cell(cell, cell_map, 'left')
        if adjacent_cell:
            adjacent_bg_color = actual_colors.get(adjacent_cell.cell_id, '#FFFFFF')
            color_difference = self.color_manager.calculate_rgb_difference(cell_bg_color, adjacent_bg_color)

            # 更新统计信息
            if statistics:
                statistics["total_borders_checked"] += 1
                statistics["max_color_difference"] = max(statistics["max_color_difference"], color_difference)

            if self._should_force_border(cell_bg_color, adjacent_bg_color):
                color_diff_result['left'] = True
                self._record_color_diff_border(cell, adjacent_cell, 'left', cell_bg_color, adjacent_bg_color,
                                             color_difference, False, color_diff_records, statistics)

        # 新增：检查边界单元格的非默认背景色边框
        self._detect_boundary_borders_for_non_default_cells(
            cell, table_model, cell_bg_color, color_diff_result
        )

        return color_diff_result

    def _record_color_diff_border(self, cell, adjacent_cell, direction, cell_color, adjacent_color,
                                 color_difference, is_header_separator, color_diff_records, statistics):
        """
        记录颜色差异触发的边框信息

        Args:
            cell: 当前单元格
            adjacent_cell: 相邻单元格
            direction: 边框方向
            cell_color: 当前单元格颜色
            adjacent_color: 相邻单元格颜色
            color_difference: 颜色差异值
            is_header_separator: 是否是表头分割线
            color_diff_records: 记录列表
            statistics: 统计信息
        """
        if color_diff_records is not None:
            record = {
                "cell_id": cell.cell_id,
                "cell_position": (cell.row_index, cell.col_index),
                "direction": direction,
                "cell_color": cell_color,
                "adjacent_cell_id": adjacent_cell.cell_id,
                "adjacent_cell_position": (adjacent_cell.row_index, adjacent_cell.col_index),
                "adjacent_color": adjacent_color,
                "color_difference": color_difference,
                "is_header_separator": is_header_separator,
                "trigger_reason": "color_difference"
            }
            color_diff_records.append(record)

        # 更新统计信息
        if statistics:
            statistics["color_diff_triggered"] += 1
            statistics["min_color_difference"] = min(statistics["min_color_difference"], color_difference)
            if is_header_separator:
                statistics["header_separator_affected"] += 1

    def _create_cell_position_map(self, table_model) -> Dict[Tuple[int, int], object]:
        """
        创建单元格位置映射
        
        为合并单元格的每个覆盖位置都建立映射，便于查找相邻单元格。
        
        Args:
            table_model: 表格模型
            
        Returns:
            位置到单元格的映射字典
        """
        cell_map = {}
        for row in table_model.rows:
            for cell in row.cells:
                # 为合并单元格的每个覆盖位置都建立映射
                for r in range(cell.row_index, cell.row_index + cell.row_span):
                    for col in range(cell.col_index, cell.col_index + cell.col_span):
                        cell_map[(r, col)] = cell
        return cell_map

    def _get_adjacent_cell(self, cell, cell_map, direction):
        """
        获取指定方向的相邻单元格
        
        Args:
            cell: 当前单元格
            cell_map: 单元格位置映射
            direction: 方向 ('top', 'right', 'bottom', 'left')
            
        Returns:
            相邻单元格，如果没有则返回None
        """
        # 根据方向确定相邻位置
        if direction == 'top':
            adjacent_row = cell.row_index - 1
            adjacent_col = cell.col_index
        elif direction == 'bottom':
            adjacent_row = cell.row_index + cell.row_span
            adjacent_col = cell.col_index
        elif direction == 'left':
            adjacent_row = cell.row_index
            adjacent_col = cell.col_index - 1
        elif direction == 'right':
            adjacent_row = cell.row_index
            adjacent_col = cell.col_index + cell.col_span
        else:
            return None
        
        # 查找相邻单元格
        return cell_map.get((adjacent_row, adjacent_col))
    
    def _should_force_border(self, color1: str, color2: str) -> bool:
        """
        判断是否应该强制显示边框
        
        根据两个颜色的差异决定是否需要边框。
        
        Args:
            color1: 第一个颜色
            color2: 第二个颜色
            
        Returns:
            如果需要边框返回True，否则返回False
        """
        # 使用RGB差值和判断颜色差异
        return not self.color_manager.are_colors_similar(color1, color2)
    
    def _is_non_default_background(self, cell_color: str) -> bool:
        """
        判断单元格是否使用非默认背景色
        
        使用颜色距离阈值的方式判断，而不是固定的颜色列表匹配。
        如果单元格背景色与标准白色的距离小于阈值，则认为是默认色。
        
        Args:
            cell_color: 单元格背景色
            
        Returns:
            True表示非默认色，False表示默认色
        """
        if not cell_color:
            return False
            
        # 标准白色作为默认色基准
        default_white = "#FFFFFF"
        
        # 使用较小的阈值进行默认色判断（比颜色相似性判断更严格）
        default_color_threshold = 15
        
        # 计算与标准白色的颜色距离
        color_distance = self.color_manager.calculate_rgb_difference(cell_color, default_white)
        
        # 距离小于阈值则认为是默认色
        return color_distance >= default_color_threshold
    
    def _detect_boundary_borders_for_non_default_cells(self, cell, table_model, cell_bg_color, color_diff_result):
        """
        为非默认背景色的边界单元格添加边框
        
        检查：
        - 第一行的上边框
        - 第一列的左边框  
        - 最后一行的下边框
        - 最后一列的右边框
        
        同时记录边缘单元格的默认色判断信息到metadata中。
        
        Args:
            cell: 当前单元格
            table_model: 表格模型
            cell_bg_color: 单元格背景色
            color_diff_result: 颜色差异边框结果字典（会被修改）
        """
        # 计算表格边界位置
        is_top_boundary = (cell.row_index == 0)
        is_left_boundary = (cell.col_index == 0)
        is_bottom_boundary = (cell.row_index + cell.row_span - 1 == table_model.num_rows - 1)
        is_right_boundary = (cell.col_index + cell.col_span - 1 == table_model.num_cols - 1)
        
        # 判断是否为边缘单元格
        is_boundary_cell = is_top_boundary or is_left_boundary or is_bottom_boundary or is_right_boundary
        
        # 如果是边缘单元格，记录其默认色判断信息
        if is_boundary_cell:
            is_default_color = not self._is_non_default_background(cell_bg_color)
            
            # 记录边缘单元格信息
            boundary_info = {
                "cell_id": cell.cell_id,
                "row_index": cell.row_index,
                "col_index": cell.col_index,
                "background_color": cell_bg_color,
                "is_default_color": is_default_color,
                "boundary_positions": {
                    "top": is_top_boundary,
                    "left": is_left_boundary,
                    "bottom": is_bottom_boundary,
                    "right": is_right_boundary
                }
            }
            
            self.boundary_cells_default_color_info[cell.cell_id] = boundary_info
        
        # 只有非默认背景色的单元格才需要边界边框
        if not self._is_non_default_background(cell_bg_color):
            return
        
        # 为边界单元格添加对应的边框
        if is_top_boundary:
            color_diff_result['top'] = True
            self.logger.debug(f"非默认背景色边界单元格 ({cell.row_index}, {cell.col_index}) 添加上边框")
            
        if is_left_boundary:
            color_diff_result['left'] = True
            self.logger.debug(f"非默认背景色边界单元格 ({cell.row_index}, {cell.col_index}) 添加左边框")
            
        if is_bottom_boundary:
            color_diff_result['bottom'] = True
            self.logger.debug(f"非默认背景色边界单元格 ({cell.row_index}, {cell.col_index}) 添加下边框")
            
        if is_right_boundary:
            color_diff_result['right'] = True
            self.logger.debug(f"非默认背景色边界单元格 ({cell.row_index}, {cell.col_index}) 添加右边框")