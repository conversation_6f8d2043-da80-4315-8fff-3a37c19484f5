"""
边框颜色工具

V4.5新增：专门处理边框颜色生成和相邻单元格颜色分析的工具类。
"""

import logging
from typing import Dict, Tuple, Optional

from .color_utils import ColorManager


class BorderColorManager:
    """
    边框颜色管理器
    
    负责为表格单元格生成合适的边框颜色，确保与相邻单元格背景色有足够对比度。
    """
    
    def __init__(self, color_manager: ColorManager):
        """
        初始化边框颜色管理器
        
        Args:
            color_manager: 颜色管理器实例
        """
        self.color_manager = color_manager
        self.logger = logging.getLogger(__name__)
    
    def generate_border_colors_for_cells(self, table_model, config, random_state):
        """
        V4.5增强版：为表格生成智能边框颜色

        根据配置的 randomize_border_color_probability 概率生成全局统一的边框颜色，
        确保边框颜色与所有单元格背景色有足够的差距。

        Args:
            table_model: 表格模型
            config: 样式配置
            random_state: 随机状态
        """
        # 获取边框颜色随机化概率
        border_color_prob = getattr(config, 'randomize_border_color_probability', 0.0)

        if border_color_prob <= 0:
            # 如果概率为0，保持默认黑色，无需处理
            return

        self.logger.debug(f"开始生成智能边框颜色，概率: {border_color_prob}")

        # 收集所有背景色
        bg_colors_list = self._get_actual_cell_colors(table_model, config)

        # 生成智能边框颜色
        global_border_color = self._generate_global_border_color(
            border_color_prob, config, random_state, bg_colors_list
        )

        # 为所有单元格的所有边框设置相同的颜色
        for row in table_model.rows:
            for cell in row.cells:
                if cell.border_style.top:
                    cell.border_style.top_color = global_border_color
                if cell.border_style.right:
                    cell.border_style.right_color = global_border_color
                if cell.border_style.bottom:
                    cell.border_style.bottom_color = global_border_color
                if cell.border_style.left:
                    cell.border_style.left_color = global_border_color

        self.logger.debug(f"智能边框颜色生成完成，使用颜色: {global_border_color}，与{len(set(bg_colors_list))}种背景色保持差距")

        # 记录边框颜色信息到metadata
        from .metadata_collector import metadata_collector
        unique_bg_colors = list(set(bg_colors_list))
        metadata_collector.record_multiple_values({
            "border_color_info": {
                "generated_border_color": global_border_color,
                "randomize_probability": border_color_prob,
                "background_colors_count": len(unique_bg_colors),
                "background_colors": unique_bg_colors,
                "min_difference_requirement": 60,
                "is_default_color": global_border_color == "#000000"
            }
        })

    def _generate_global_border_color(self, border_color_prob: float, config, random_state, bg_colors_list) -> str:
        """
        V4.5增强：生成智能的全局边框颜色

        Args:
            border_color_prob: 边框颜色随机化概率
            config: 样式配置
            random_state: 随机状态
            bg_colors_list: 所有背景色列表

        Returns:
            边框颜色字符串
        """
        # 根据概率决定是否使用随机颜色
        if random_state.random() > border_color_prob:
            # 使用默认黑色
            return "#000000"

        # 使用新的智能生成策略
        return self.color_manager.generate_border_color_with_min_difference(
            bg_colors_list, min_difference=60, config=config, random_state=random_state
        )

    def _get_actual_cell_colors(self, table_model, config):
        """
        获取表格中所有实际使用的单元格背景色

        Args:
            table_model: 表格模型
            config: 样式配置

        Returns:
            背景色列表
        """
        bg_colors = []

        # 遍历所有单元格，收集背景色
        for row in table_model.rows:
            for cell in row.cells:
                if cell.is_header:
                    # 表头背景色
                    bg_color = config.header.background_color
                else:
                    # 表体背景色
                    bg_color = config.body.background_color

                bg_colors.append(bg_color)

        # 处理斑马纹颜色
        if config.zebra_stripes and config.zebra_colors:
            # 添加斑马纹的两种颜色
            bg_colors.extend(config.zebra_colors)

        return bg_colors
