"""
边框颜色工具

V4.5新增：专门处理边框颜色生成和相邻单元格颜色分析的工具类。
"""

import logging
from typing import Dict, Tuple, Optional

from .color_utils import ColorManager


class BorderColorManager:
    """
    边框颜色管理器
    
    负责为表格单元格生成合适的边框颜色，确保与相邻单元格背景色有足够对比度。
    """
    
    def __init__(self, color_manager: ColorManager):
        """
        初始化边框颜色管理器
        
        Args:
            color_manager: 颜色管理器实例
        """
        self.color_manager = color_manager
        self.logger = logging.getLogger(__name__)
    
    def generate_border_colors_for_cells(self, table_model, config, random_state):
        """
        V4.5简化版：为表格生成统一的边框颜色

        根据配置的 randomize_border_color_probability 概率生成全局统一的边框颜色。

        Args:
            table_model: 表格模型
            config: 样式配置
            random_state: 随机状态
        """
        # 获取边框颜色随机化概率
        border_color_prob = getattr(config, 'randomize_border_color_probability', 0.0)

        if border_color_prob <= 0:
            # 如果概率为0，保持默认黑色，无需处理
            return

        self.logger.debug(f"开始生成全局边框颜色，概率: {border_color_prob}")

        # V4.5简化：生成一个全局的边框颜色
        global_border_color = self._generate_global_border_color(border_color_prob, config, random_state)

        # 为所有单元格的所有边框设置相同的颜色
        for row in table_model.rows:
            for cell in row.cells:
                if cell.border_style.top:
                    cell.border_style.top_color = global_border_color
                if cell.border_style.right:
                    cell.border_style.right_color = global_border_color
                if cell.border_style.bottom:
                    cell.border_style.bottom_color = global_border_color
                if cell.border_style.left:
                    cell.border_style.left_color = global_border_color

        self.logger.debug(f"边框颜色生成完成，使用颜色: {global_border_color}")

    def _generate_global_border_color(self, border_color_prob: float, config, random_state) -> str:
        """
        V4.5新增：生成全局统一的边框颜色

        Args:
            border_color_prob: 边框颜色随机化概率
            config: 样式配置
            random_state: 随机状态

        Returns:
            边框颜色字符串
        """
        # 根据概率决定是否使用随机颜色
        if random_state.random() > border_color_prob:
            # 使用默认黑色
            return "#000000"

        # 生成随机边框颜色
        color_contrast_config = getattr(config, 'color_contrast', None)
        if color_contrast_config and random_state.random() < color_contrast_config.use_soft_colors_probability:
            # 使用柔和颜色
            return self.color_manager.generate_soft_color(random_state=random_state)
        else:
            # 使用随机颜色
            return self.color_manager._generate_random_color(random_state=random_state)
