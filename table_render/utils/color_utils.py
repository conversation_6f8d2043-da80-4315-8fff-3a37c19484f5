"""
颜色管理工具

提供智能颜色生成、对比度计算和颜色搭配功能，确保文本清晰可读。
"""

import logging
import colorsys
import random
from typing import Tuple, List, Optional
import re


class ColorManager:
    """
    颜色管理器类
    
    负责生成柔和颜色、计算对比度和确保颜色搭配的可读性。
    """
    
    def __init__(self):
        """初始化颜色管理器"""
        self.logger = logging.getLogger(__name__)
        
        # 预设的柔和色板（移除与纯白色差距<=60的颜色）
        self._soft_color_palette = [
            "#ADB5BD",  # RGB(173,181,189) 差距:(255-173)+(255-181)+(255-189)=82+74+66=222
            "#6C757D", "#495057", "#343A40", "#212529",
            "#FCF4A3",  # RGB(252,244,163) 差距:(255-252)+(255-244)+(255-163)=3+11+92=106
            "#D4EDDA",  # RGB(212,237,218) 差距:(255-212)+(255-237)+(255-218)=43+18+37=98
            "#D1ECF1",  # RGB(209,236,241) 差距:(255-209)+(255-236)+(255-241)=46+19+14=79
            "#E2E3F3",  # RGB(226,227,243) 差距:(255-226)+(255-227)+(255-243)=29+28+12=69
            "#F8D7DA",  # RGB(248,215,218) 差距:(255-248)+(255-215)+(255-218)=7+40+37=84
            "#F5C6CB",  # RGB(245,198,203) 差距:(255-245)+(255-198)+(255-203)=10+57+52=119
            "#FADBD8",  # RGB(250,219,216) 差距:(255-250)+(255-219)+(255-216)=5+36+39=80
            "#EBDEF0",  # RGB(235,222,240) 差距:(255-235)+(255-222)+(255-240)=20+33+15=68
            "#D5DBDB"   # RGB(213,219,219) 差距:(255-213)+(255-219)+(255-219)=42+36+36=114
        ]
        
        # 深色文本颜色
        self._dark_text_colors = [
            "#212529", "#343A40", "#495057", "#6C757D",
            "#2C3E50", "#34495E", "#1B2631", "#17202A"
        ]
        
        # 浅色背景颜色（移除与纯白色差距<=60的颜色）
        self._light_bg_colors = [
            "#FFFFFF",  # RGB(255,255,255) 差距:0 - 保留纯白色作为基准
            "#DEE2E6",  # RGB(222,226,230) 差距:(255-222)+(255-226)+(255-230)=33+29+25=87 ✓
            "#D6EAF8",  # RGB(214,234,248) 差距:(255-214)+(255-234)+(255-248)=41+21+7=69 ✓
            "#D5F4E6",  # RGB(213,244,230) 差距:(255-213)+(255-244)+(255-230)=42+11+25=78 ✓
            "#FCF3CF",  # RGB(252,243,207) 差距:(255-252)+(255-243)+(255-207)=3+12+48=63 ✓
            "#FADBD8",  # RGB(250,219,216) 差距:(255-250)+(255-219)+(255-216)=5+36+39=80 ✓
            "#E8DAEF",  # RGB(232,218,239) 差距:(255-232)+(255-218)+(255-239)=23+37+16=76 ✓
            "#D5DBDB",  # RGB(213,219,219) 差距:(255-213)+(255-219)+(255-219)=42+36+36=114 ✓
            "#AED6F1",  # RGB(174,214,241) 差距:(255-174)+(255-214)+(255-241)=81+41+14=136 ✓
            "#A9DFBF",  # RGB(169,223,191) 差距:(255-169)+(255-223)+(255-191)=86+32+64=182 ✓
        ]
    
    def hex_to_rgb(self, hex_color: str) -> Tuple[int, int, int]:
        """
        将十六进制颜色转换为RGB
        
        Args:
            hex_color: 十六进制颜色字符串（如 "#FF0000"）
            
        Returns:
            RGB元组 (r, g, b)
        """
        hex_color = hex_color.lstrip('#')
        if len(hex_color) != 6:
            raise ValueError(f"无效的十六进制颜色: {hex_color}")
        
        return tuple(int(hex_color[i:i+2], 16) for i in (0, 2, 4))
    
    def rgb_to_hex(self, r: int, g: int, b: int) -> str:
        """
        将RGB转换为十六进制颜色
        
        Args:
            r, g, b: RGB值 (0-255)
            
        Returns:
            十六进制颜色字符串
        """
        return f"#{r:02x}{g:02x}{b:02x}"
    
    def calculate_luminance(self, rgb: Tuple[int, int, int]) -> float:
        """
        计算颜色的相对亮度
        
        Args:
            rgb: RGB元组
            
        Returns:
            相对亮度值 (0-1)
        """
        def normalize_channel(channel):
            channel = channel / 255.0
            if channel <= 0.03928:
                return channel / 12.92
            else:
                return pow((channel + 0.055) / 1.055, 2.4)
        
        r, g, b = rgb
        r_norm = normalize_channel(r)
        g_norm = normalize_channel(g)
        b_norm = normalize_channel(b)
        
        return 0.2126 * r_norm + 0.7152 * g_norm + 0.0722 * b_norm
    
    def calculate_contrast_ratio(self, color1: str, color2: str) -> float:
        """
        计算两个颜色的对比度比例
        
        Args:
            color1: 第一个颜色（十六进制）
            color2: 第二个颜色（十六进制）
            
        Returns:
            对比度比例 (1-21)
        """
        try:
            rgb1 = self.hex_to_rgb(color1)
            rgb2 = self.hex_to_rgb(color2)
            
            lum1 = self.calculate_luminance(rgb1)
            lum2 = self.calculate_luminance(rgb2)
            
            # 确保较亮的颜色在分子位置
            if lum1 < lum2:
                lum1, lum2 = lum2, lum1
            
            return (lum1 + 0.05) / (lum2 + 0.05)
        except Exception as e:
            self.logger.warning(f"计算对比度失败: {e}")
            return 1.0
    
    def generate_soft_color(self, saturation_range=(0.1, 0.6), lightness_range=(0.3, 0.9), random_state=None) -> str:
        """
        生成柔和颜色（低饱和度、中等亮度）
        
        Args:
            saturation_range: 饱和度范围
            lightness_range: 亮度范围
            random_state: 随机状态，如果为None则使用random模块
            
        Returns:
            柔和颜色的十六进制字符串
        """
        # 使用HSL颜色空间生成柔和颜色
        if random_state is None:
            hue = random.random()  # 色相随机
            saturation = random.uniform(*saturation_range)  # 低饱和度
            lightness = random.uniform(*lightness_range)    # 中等亮度
        else:
            hue = random_state.random()  # 色相随机
            saturation = random_state.uniform(*saturation_range)  # 低饱和度
            lightness = random_state.uniform(*lightness_range)    # 中等亮度
        
        # 转换为RGB
        r, g, b = colorsys.hls_to_rgb(hue, lightness, saturation)
        
        return self.rgb_to_hex(int(r * 255), int(g * 255), int(b * 255))
        
    def _generate_random_color(self, random_state=None) -> str:
        """
        生成完全随机的颜色代码
        
        Args:
            random_state: 随机状态，如果为None则使用random模块
        
        Returns:
            十六进制颜色字符串
        """
        if random_state is None:
            r = random.randint(0, 230)
            g = random.randint(0, 230)
            b = random.randint(0, 230)
        else:
            r = random_state.randint(0, 230)
            g = random_state.randint(0, 230)
            b = random_state.randint(0, 230)
        return self.rgb_to_hex(r, g, b)
    
    def ensure_readable_combination(self, text_color: str, bg_color: str, 
                                  min_contrast: float = 4.5, random_state=None) -> Tuple[str, str]:
        """
        确保颜色组合满足可读性要求
        
        Args:
            text_color: 文本颜色
            bg_color: 背景颜色
            min_contrast: 最小对比度要求
            random_state: 随机状态，如果为None则使用random模块
            
        Returns:
            调整后的 (文本颜色, 背景颜色) 元组
        """
        current_contrast = self.calculate_contrast_ratio(text_color, bg_color)
        
        if current_contrast >= min_contrast:
            return text_color, bg_color
        
        self.logger.debug(f"对比度不足 ({current_contrast:.2f} < {min_contrast})，正在调整颜色")
        
        # 如果对比度不足，使用预设的高对比度组合
        return self._get_high_contrast_combination(random_state=random_state)
    
    def _get_high_contrast_combination(self, random_state=None) -> Tuple[str, str]:
        """
        获取高对比度颜色组合
        
        Args:
            random_state: 随机状态，如果为None则使用random模块
        
        Returns:
            (文本颜色, 背景颜色) 元组
        """
        # 随机选择深色文本和浅色背景的组合
        if random_state is None:
            text_color = random.choice(self._dark_text_colors)
            bg_color = random.choice(self._light_bg_colors)
        else:
            text_color = random_state.choice(self._dark_text_colors)
            bg_color = random_state.choice(self._light_bg_colors)
        return text_color, bg_color
    
    def get_color_palette(self, palette_type: str = "soft") -> List[str]:
        """
        获取预设色板
        
        Args:
            palette_type: 色板类型 ("soft", "dark_text", "light_bg")
            
        Returns:
            颜色列表
        """
        if palette_type == "soft":
            return self._soft_color_palette.copy()
        elif palette_type == "dark_text":
            return self._dark_text_colors.copy()
        elif palette_type == "light_bg":
            return self._light_bg_colors.copy()
        else:
            self.logger.warning(f"未知的色板类型: {palette_type}")
            return self._soft_color_palette.copy()
    
    def get_color_pair(self, randomize_color_probability: float, color_contrast_config, random_state=None) -> Tuple[str, str]:
        """
        V3.3重构：根据概率和颜色对比度配置获取颜色对

        Args:
            randomize_color_probability: 随机化概率，如果随机数大于此值则返回默认色
            color_contrast_config: 颜色对比度配置对象
            random_state: 随机状态，如果为None则使用random模块

        Returns:
            (文本颜色, 背景颜色) 元组
        """
        if random_state is None:
            rand_val = random.random()
        else:
            rand_val = random_state.random()

        if rand_val > randomize_color_probability:
            # 3.1 默认颜色：黑字白底
            return "#000000", "#FFFFFF"
        else:
            # 3.2 触发随机颜色
            # 3.3/3.4 根据use_soft_colors_probability概率选择颜色生成策略
            if random_state is None:
                soft_color_rand = random.random()
            else:
                soft_color_rand = random_state.random()

            if soft_color_rand < color_contrast_config.use_soft_colors_probability:
                # 3.3 柔和颜色策略：不区分背景和字，都调用同一个函数
                text_color = self.generate_soft_color(random_state=random_state)
                bg_color = self.generate_soft_color(random_state=random_state)
            else:
                # 3.4 非柔和颜色策略：随机生成颜色代码
                text_color = self._generate_random_color(random_state=random_state)
                bg_color = self._generate_random_color(random_state=random_state)

            # 确保满足最小对比度要求
        return self.ensure_readable_combination(
            text_color, bg_color, color_contrast_config.min_contrast_ratio, random_state
        )

    def adjust_color_brightness(self, color: str, factor: float) -> str:
        """
        调整颜色亮度

        Args:
            color: 十六进制颜色
            factor: 调整因子 (>1变亮, <1变暗)

        Returns:
            调整后的颜色
        """
        try:
            r, g, b = self.hex_to_rgb(color)
            h, l, s = colorsys.rgb_to_hls(r/255, g/255, b/255)

            # 调整亮度
            l = max(0, min(1, l * factor))

            r, g, b = colorsys.hls_to_rgb(h, l, s)
            r, g, b = int(r * 255), int(g * 255), int(b * 255)

            return self.rgb_to_hex(r, g, b)
        except Exception as e:
            self.logger.warning(f"调整颜色亮度失败: {e}")
            return color

    def get_inherited_color_pair(self, header_text_color: str, header_bg_color: str,
                                text_color_change_prob: float, bg_color_change_prob: float,
                                randomize_color_probability: float, color_contrast_config,
                                random_state=None) -> Tuple[str, str]:
        """
        V3.3修正：统一的颜色继承逻辑

        无论表头是什么颜色，都遵循相同的继承流程：
        1. 首先继承表头颜色
        2. 根据变化概率判断是否要改变文本色/背景色
        3. 如果需要改变，则生成新颜色
        4. 确保最终颜色组合满足对比度要求

        Args:
            header_text_color: 表头文本颜色
            header_bg_color: 表头背景颜色
            text_color_change_prob: 文本颜色变化概率
            bg_color_change_prob: 背景颜色变化概率
            randomize_color_probability: 随机化概率
            color_contrast_config: 颜色对比度配置
            random_state: 随机状态

        Returns:
            (文本颜色, 背景颜色) 元组
        """
        if random_state is None:
            rand_text = random.random()
            rand_bg = random.random()
        else:
            rand_text = random_state.random()
            rand_bg = random_state.random()

        # 统一继承逻辑：无论表头是什么颜色，都先继承
        current_text = header_text_color
        current_bg = header_bg_color

        # 检查表头是否为默认色，用于优化颜色生成逻辑
        header_is_default = (header_text_color == "#000000" and header_bg_color == "#FFFFFF")

        # 独立判断是否要改变文本色
        text_color_changed = False
        if rand_text < text_color_change_prob:
            # 优化：如果表头是默认色且要改变，强制生成随机颜色（避免又生成默认色）
            effective_randomize_prob = 1.0 if header_is_default else randomize_color_probability
            new_text, _ = self.get_color_pair(effective_randomize_prob, color_contrast_config, random_state)
            current_text = new_text
            text_color_changed = True

        # 独立判断是否要改变背景色
        background_color_changed = False
        if rand_bg < bg_color_change_prob:
            # 优化：如果表头是默认色且要改变，强制生成随机颜色（避免又生成默认色）
            effective_randomize_prob = 1.0 if header_is_default else randomize_color_probability
            _, new_bg = self.get_color_pair(effective_randomize_prob, color_contrast_config, random_state)
            current_bg = new_bg
            background_color_changed = True

        # 记录颜色继承变化信息到metadata
        from .metadata_collector import metadata_collector
        metadata_collector.record_multiple_values({
            "inheritance_text_color_changed": text_color_changed,
            "inheritance_background_color_changed": background_color_changed
        })

        # 确保最终的颜色组合满足对比度要求
        return self.ensure_readable_combination(
            current_text, current_bg, color_contrast_config.min_contrast_ratio
        )

    def calculate_rgb_difference(self, color1: str, color2: str) -> int:
        """
        V4.5新增：计算两个颜色的RGB差值和

        Args:
            color1: 第一个颜色（十六进制）
            color2: 第二个颜色（十六进制）

        Returns:
            RGB差值和：|R1-R2| + |G1-G2| + |B1-B2|
        """
        try:
            r1, g1, b1 = self.hex_to_rgb(color1)
            r2, g2, b2 = self.hex_to_rgb(color2)

            return abs(r1 - r2) + abs(g1 - g2) + abs(b1 - b2)
        except Exception as e:
            self.logger.warning(f"计算RGB差值失败: {e}")
            return 0

    def are_colors_similar(self, color1: str, color2: str, threshold: int = 40) -> bool:
        """
        V4.5新增：判断两个颜色是否相似

        Args:
            color1: 第一个颜色（十六进制）
            color2: 第二个颜色（十六进制）
            threshold: 相似度阈值，默认为10

        Returns:
            如果颜色相似返回True，否则返回False
        """
        return self.calculate_rgb_difference(color1, color2) < threshold

    def adjust_color_for_difference(self, target_color: str, reference_color: str,
                                   min_difference: int = 60) -> str:
        """
        调整目标颜色使其与参考颜色的差距达到最小要求

        Args:
            target_color: 需要调整的颜色
            reference_color: 参考颜色
            min_difference: 最小RGB差距要求

        Returns:
            调整后的颜色
        """
        current_diff = self.calculate_rgb_difference(target_color, reference_color)

        if current_diff >= min_difference:
            return target_color  # 已经满足要求

        # 需要增加的差距
        needed_diff = min_difference - current_diff

        target_r, target_g, target_b = self.hex_to_rgb(target_color)
        ref_r, ref_g, ref_b = self.hex_to_rgb(reference_color)

        # 智能判断调整方向：选择能产生最大差距增量的方向
        # 策略：如果目标色的某个通道已经比参考色大，继续增大；如果小，继续减小

        adjustments = []

        # 对每个RGB通道计算最佳调整方向
        for target_val, ref_val in [(target_r, ref_r), (target_g, ref_g), (target_b, ref_b)]:
            if target_val > ref_val:
                # 目标值更大，继续增大以扩大差距
                adjustments.append(1)  # 增加
            else:
                # 目标值更小，继续减小以扩大差距
                adjustments.append(-1)  # 减少

        # 计算每个通道需要调整的幅度
        # 简单策略：平均分配needed_diff到三个通道
        per_channel_adjustment = needed_diff // 3
        remainder = needed_diff % 3

        # 应用调整
        new_r = target_r + adjustments[0] * (per_channel_adjustment + (1 if remainder > 0 else 0))
        new_g = target_g + adjustments[1] * (per_channel_adjustment + (1 if remainder > 1 else 0))
        new_b = target_b + adjustments[2] * per_channel_adjustment

        # 确保RGB值在有效范围内
        new_r = max(0, min(255, new_r))
        new_g = max(0, min(255, new_g))
        new_b = max(0, min(255, new_b))

        return self.rgb_to_hex(new_r, new_g, new_b)

    def is_default_color_pair(self, text_color: str, bg_color: str) -> bool:
        """
        V4.5新增：判断是否为默认颜色组合（黑字白底）

        Args:
            text_color: 文本颜色
            bg_color: 背景颜色

        Returns:
            如果是默认颜色组合返回True，否则返回False
        """
        return text_color.upper() == "#000000" and bg_color.upper() == "#FFFFFF"
