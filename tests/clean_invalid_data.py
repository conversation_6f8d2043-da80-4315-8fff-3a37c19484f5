#!/usr/bin/env python3
"""
数据清理脚本 - 删除无效的生成数据

该脚本用于清理TableRender生成的数据，删除实际图像尺寸超出metadata中
background_dimensions记录尺寸的无效样本。

使用方法:
    python clean_invalid_data.py --input_dir /path/to/generated_data

输入目录结构:
    input_dir/
    ├── images/          # PNG图像文件
    ├── annotations/     # JSON标注文件  
    └── metadata/        # JSON元数据文件
"""

import os
import json
import argparse
from pathlib import Path
from PIL import Image
import logging

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


def setup_logging(verbose=False):
    """设置日志级别"""
    if verbose:
        logging.getLogger().setLevel(logging.DEBUG)


def validate_input_directory(input_dir):
    """验证输入目录结构"""
    input_path = Path(input_dir)
    
    if not input_path.exists():
        raise FileNotFoundError(f"输入目录不存在: {input_dir}")
    
    required_subdirs = ['images', 'annotations', 'metadata']
    missing_dirs = []
    
    for subdir in required_subdirs:
        subdir_path = input_path / subdir
        if not subdir_path.exists():
            missing_dirs.append(subdir)
    
    if missing_dirs:
        raise FileNotFoundError(f"缺少必需的子目录: {missing_dirs}")
    
    logger.info(f"输入目录验证通过: {input_dir}")
    return input_path


def get_background_dimensions(metadata_file):
    """从元数据文件中提取background_dimensions"""
    try:
        with open(metadata_file, 'r', encoding='utf-8') as f:
            metadata = json.load(f)
        
        # 提取 css_render_info.background_dimensions
        css_render_info = metadata.get('css_render_info', {})
        background_dimensions = css_render_info.get('background_dimensions', {})
        
        width = background_dimensions.get('width')
        height = background_dimensions.get('height')
        
        if width is None or height is None:
            raise ValueError(f"background_dimensions中缺少width或height字段")
        
        return float(width), float(height)
        
    except Exception as e:
        raise Exception(f"读取元数据文件失败 {metadata_file}: {str(e)}")


def get_image_dimensions(image_file):
    """获取图像的实际尺寸"""
    try:
        with Image.open(image_file) as img:
            return img.size  # (width, height)
    except Exception as e:
        raise Exception(f"读取图像文件失败 {image_file}: {str(e)}")


def should_delete_sample(image_file, metadata_file):
    """判断是否应该删除该样本"""
    try:
        # 获取元数据中的预期尺寸
        expected_width, expected_height = get_background_dimensions(metadata_file)
        
        # 获取实际图像尺寸
        actual_width, actual_height = get_image_dimensions(image_file)
        
        # 检查是否超出边界
        width_overflow = actual_width > expected_width
        height_overflow = actual_height > expected_height
        
        if width_overflow or height_overflow:
            logger.debug(f"样本超出边界:")
            logger.debug(f"  文件: {image_file.name}")
            logger.debug(f"  预期尺寸: {expected_width}x{expected_height}")
            logger.debug(f"  实际尺寸: {actual_width}x{actual_height}")
            logger.debug(f"  宽度超出: {width_overflow}, 高度超出: {height_overflow}")
            return True
        
        return False
        
    except Exception as e:
        logger.error(f"检查样本时出错: {str(e)}")
        raise


def delete_sample_files(basename, input_path):
    """删除样本的所有相关文件"""
    files_to_delete = [
        input_path / 'images' / f'{basename}.png',
        input_path / 'annotations' / f'{basename}.json',
        input_path / 'metadata' / f'{basename}.json'
    ]
    
    deleted_files = []
    for file_path in files_to_delete:
        if file_path.exists():
            try:
                file_path.unlink()
                deleted_files.append(str(file_path))
                logger.debug(f"已删除: {file_path}")
            except Exception as e:
                logger.error(f"删除文件失败 {file_path}: {str(e)}")
                raise
        else:
            logger.warning(f"文件不存在，跳过删除: {file_path}")
    
    return deleted_files


def clean_invalid_data(input_dir, dry_run=False):
    """清理无效数据的主函数"""
    input_path = validate_input_directory(input_dir)
    
    images_dir = input_path / 'images'
    metadata_dir = input_path / 'metadata'
    
    # 获取所有PNG图像文件
    image_files = list(images_dir.glob('*.png'))
    
    if not image_files:
        logger.warning("未找到任何PNG图像文件")
        return
    
    logger.info(f"找到 {len(image_files)} 个图像文件")
    
    # 统计信息
    total_files = len(image_files)
    invalid_count = 0
    deleted_files_count = 0
    
    for image_file in image_files:
        basename = image_file.stem  # 不包含扩展名的文件名
        metadata_file = metadata_dir / f'{basename}.json'
        
        try:
            # 检查元数据文件是否存在
            if not metadata_file.exists():
                logger.error(f"元数据文件不存在: {metadata_file}")
                continue
            
            # 判断是否需要删除
            if should_delete_sample(image_file, metadata_file):
                invalid_count += 1
                
                if dry_run:
                    logger.info(f"[DRY RUN] 将删除样本: {basename}")
                else:
                    deleted_files = delete_sample_files(basename, input_path)
                    deleted_files_count += len(deleted_files)
                    logger.info(f"已删除无效样本: {basename}")
            
        except Exception as e:
            logger.error(f"处理样本 {basename} 时出错: {str(e)}")
            continue
    
    # 输出统计结果
    logger.info("=" * 50)
    logger.info("清理完成统计:")
    logger.info(f"  总样本数: {total_files}")
    logger.info(f"  无效样本数: {invalid_count}")
    if not dry_run:
        logger.info(f"  删除文件数: {deleted_files_count}")
    logger.info(f"  有效样本数: {total_files - invalid_count}")
    logger.info(f"  无效率: {invalid_count/total_files*100:.2f}%")


def main():
    """主函数"""
    parser = argparse.ArgumentParser(
        description='清理TableRender生成的无效数据',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
示例用法:
  python clean_invalid_data.py --input_dir ./generated_data
  python clean_invalid_data.py --input_dir ./generated_data --dry-run
  python clean_invalid_data.py --input_dir ./generated_data --verbose
        """
    )
    
    parser.add_argument(
        '--input_dir',
        type=str,
        required=True,
        help='生成数据的根目录路径'
    )
    
    parser.add_argument(
        '--dry-run',
        action='store_true',
        help='干运行模式，只显示会被删除的文件而不实际删除'
    )
    
    parser.add_argument(
        '--verbose',
        action='store_true',
        help='启用详细日志输出'
    )
    
    args = parser.parse_args()
    
    # 设置日志级别
    setup_logging(args.verbose)
    
    try:
        logger.info("开始清理无效数据...")
        if args.dry_run:
            logger.info("运行模式: DRY RUN (不会实际删除文件)")
        
        clean_invalid_data(args.input_dir, args.dry_run)
        
        logger.info("数据清理完成!")
        
    except Exception as e:
        logger.error(f"清理过程中发生错误: {str(e)}")
        return 1
    
    return 0


if __name__ == '__main__':
    exit(main())
