#!/usr/bin/env python3
"""
数据清理和验证脚本 - 删除无效数据并验证表格结构

该脚本用于：
1. 清理TableRender生成的数据，删除实际图像尺寸超出metadata中background_dimensions记录尺寸的无效样本
2. 验证表格样本的结构质量，检测坐标异常、覆盖率等问题

使用方法:
    python clean_invalid_data.py --input_dir /path/to/generated_data

输入目录结构:
    input_dir/
    ├── images/          # PNG图像文件
    ├── annotations/     # JSON标注文件
    ├── metadata/        # JSON元数据文件
    └── *.json           # 表格标注文件（包含cells字段）

输出文件:
    - invalid_table_samples.json  # 无效表格样本列表（新增）
"""

import os
import json
import argparse
import glob
from pathlib import Path
from PIL import Image
import logging
from tqdm import tqdm
import numpy as np

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


def setup_logging(verbose=False):
    """设置日志级别"""
    if verbose:
        logging.getLogger().setLevel(logging.DEBUG)


def validate_input_directory(input_dir):
    """验证输入目录结构"""
    input_path = Path(input_dir)
    
    if not input_path.exists():
        raise FileNotFoundError(f"输入目录不存在: {input_dir}")
    
    required_subdirs = ['images', 'annotations', 'metadata']
    missing_dirs = []
    
    for subdir in required_subdirs:
        subdir_path = input_path / subdir
        if not subdir_path.exists():
            missing_dirs.append(subdir)
    
    if missing_dirs:
        raise FileNotFoundError(f"缺少必需的子目录: {missing_dirs}")
    
    logger.info(f"输入目录验证通过: {input_dir}")
    return input_path


def get_background_dimensions(metadata_file):
    """从元数据文件中提取background_dimensions"""
    try:
        with open(metadata_file, 'r', encoding='utf-8') as f:
            metadata = json.load(f)
        
        # 提取 css_render_info.background_dimensions
        css_render_info = metadata.get('css_render_info', {})
        background_dimensions = css_render_info.get('background_dimensions', {})
        
        width = background_dimensions.get('width')
        height = background_dimensions.get('height')
        
        if width is None or height is None:
            raise ValueError(f"background_dimensions中缺少width或height字段")
        
        return float(width), float(height)
        
    except Exception as e:
        raise Exception(f"读取元数据文件失败 {metadata_file}: {str(e)}")


def get_image_dimensions(image_file):
    """获取图像的实际尺寸"""
    try:
        with Image.open(image_file) as img:
            return img.size  # (width, height)
    except Exception as e:
        raise Exception(f"读取图像文件失败 {image_file}: {str(e)}")


def should_delete_sample(image_file, metadata_file):
    """判断是否应该删除该样本"""
    try:
        # 获取元数据中的预期尺寸
        expected_width, expected_height = get_background_dimensions(metadata_file)
        
        # 获取实际图像尺寸
        actual_width, actual_height = get_image_dimensions(image_file)
        
        # 检查是否超出边界
        width_overflow = actual_width > expected_width
        height_overflow = actual_height > expected_height
        
        if width_overflow or height_overflow:
            logger.debug(f"样本超出边界:")
            logger.debug(f"  文件: {image_file.name}")
            logger.debug(f"  预期尺寸: {expected_width}x{expected_height}")
            logger.debug(f"  实际尺寸: {actual_width}x{actual_height}")
            logger.debug(f"  宽度超出: {width_overflow}, 高度超出: {height_overflow}")
            return True
        
        return False
        
    except Exception as e:
        logger.error(f"检查样本时出错: {str(e)}")
        raise


def delete_sample_files(basename, input_path):
    """删除样本的所有相关文件"""
    files_to_delete = [
        input_path / 'images' / f'{basename}.png',
        input_path / 'annotations' / f'{basename}.json',
        input_path / 'metadata' / f'{basename}.json'
    ]
    
    deleted_files = []
    for file_path in files_to_delete:
        if file_path.exists():
            try:
                file_path.unlink()
                deleted_files.append(str(file_path))
                logger.debug(f"已删除: {file_path}")
            except Exception as e:
                logger.error(f"删除文件失败 {file_path}: {str(e)}")
                raise
        else:
            logger.warning(f"文件不存在，跳过删除: {file_path}")
    
    return deleted_files


def clean_invalid_data(input_dir, dry_run=False):
    """清理无效数据的主函数"""
    input_path = validate_input_directory(input_dir)
    
    images_dir = input_path / 'images'
    metadata_dir = input_path / 'metadata'
    
    # 获取所有PNG图像文件
    image_files = list(images_dir.glob('*.png'))
    
    if not image_files:
        logger.warning("未找到任何PNG图像文件")
        return
    
    logger.info(f"找到 {len(image_files)} 个图像文件")
    
    # 统计信息
    total_files = len(image_files)
    invalid_count = 0
    deleted_files_count = 0
    
    for image_file in image_files:
        basename = image_file.stem  # 不包含扩展名的文件名
        metadata_file = metadata_dir / f'{basename}.json'
        
        try:
            # 检查元数据文件是否存在
            if not metadata_file.exists():
                logger.error(f"元数据文件不存在: {metadata_file}")
                continue
            
            # 判断是否需要删除
            if should_delete_sample(image_file, metadata_file):
                invalid_count += 1
                
                if dry_run:
                    logger.info(f"[DRY RUN] 将删除样本: {basename}")
                else:
                    deleted_files = delete_sample_files(basename, input_path)
                    deleted_files_count += len(deleted_files)
                    logger.info(f"已删除无效样本: {basename}")
            
        except Exception as e:
            logger.error(f"处理样本 {basename} 时出错: {str(e)}")
            continue
    
    # 输出统计结果
    logger.info("=" * 50)
    logger.info("清理完成统计:")
    logger.info(f"  总样本数: {total_files}")
    logger.info(f"  无效样本数: {invalid_count}")
    if not dry_run:
        logger.info(f"  删除文件数: {deleted_files_count}")
    logger.info(f"  有效样本数: {total_files - invalid_count}")
    logger.info(f"  无效率: {invalid_count/total_files*100:.2f}%")


def find_table_sample_pairs(input_dir):
    """
    在input_dir中找到表格样本文件对

    识别规则：
    - 标注文件：*.json (包含'cells'字段)
    - metadata文件：*_metadata.json
    """
    pairs = []
    input_path = Path(input_dir)

    # 在所有子目录中查找JSON文件
    json_files = []
    for pattern in ['*.json', '*/*.json', '*/*/*.json']:
        json_files.extend(glob.glob(str(input_path / pattern)))

    for json_file in json_files:
        if json_file.endswith("_metadata.json"):
            continue

        # 尝试找对应的metadata文件
        json_path = Path(json_file)
        base_name = json_path.stem

        # 移除可能的后缀（如bug）
        if base_name.endswith('bug'):
            base_name = base_name[:-3]

        # 在同一目录和相邻目录查找metadata文件
        possible_metadata_paths = [
            json_path.parent / f"{base_name}_metadata.json",
            json_path.parent / f"{json_path.stem}_metadata.json",
        ]

        # 如果在子目录中，也尝试在父目录查找
        if json_path.parent != input_path:
            possible_metadata_paths.extend([
                input_path / f"{base_name}_metadata.json",
                input_path / "metadata" / f"{base_name}.json",
            ])

        metadata_file = None
        for meta_path in possible_metadata_paths:
            if meta_path.exists():
                metadata_file = str(meta_path)
                break

        if metadata_file:
            # 简单验证是否是表格文件
            try:
                with open(json_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    if 'cells' in data:  # 简单特征检查
                        pairs.append((json_file, metadata_file))
            except:
                continue

    return pairs


def check_coordinate_consistency(annotations):
    """检查坐标一致性 - 核心检查逻辑"""
    issues = []
    cells = annotations.get('cells', [])

    # 收集宽度信息，包含位置信息
    width_info = []
    for cell in cells:
        if 'bbox' not in cell or 'lloc' not in cell:
            continue

        bbox = cell['bbox']
        lloc = cell['lloc']
        width = bbox['p2'][0] - bbox['p1'][0]
        col_span = lloc['end_col'] - lloc['start_col'] + 1

        # 获取单元格位置信息
        row_pos = lloc.get('start_row', -1)
        col_pos = lloc.get('start_col', -1)
        cell_ind = cell.get('cell_ind', -1)

        width_info.append({
            'cell_ind': cell_ind,
            'width': width,
            'col_span': col_span,
            'row': row_pos,
            'col': col_pos,
            'content': cell.get('content', '')[:10]  # 截取前10个字符作为标识
        })

    # 检查异常：不同跨度但相似宽度
    for i, cell_a in enumerate(width_info):
        for cell_b in width_info[i+1:]:
            if (cell_a['col_span'] != cell_b['col_span'] and
                abs(cell_a['width'] - cell_b['width']) / max(cell_a['width'], cell_b['width']) < 0.1):

                # 生成详细的位置描述
                desc_a = f"第{cell_a['row']}行第{cell_a['col']}列"
                desc_b = f"第{cell_b['row']}行第{cell_b['col']}列"

                if cell_a['content']:
                    desc_a += f"('{cell_a['content']}')"
                if cell_b['content']:
                    desc_b += f"('{cell_b['content']}')"

                issues.append(f"{desc_a}跨{cell_a['col_span']}列(宽{cell_a['width']:.1f})与"
                            f"{desc_b}跨{cell_b['col_span']}列(宽{cell_b['width']:.1f})宽度异常相近")
                break  # 只报告第一个问题，避免过多输出

    return issues


def calculate_coverage_ratio(annotations, metadata):
    """计算表格覆盖率"""
    expected_cells = metadata.get('rows', 1) * metadata.get('cols', 1)
    actual_positions = set()

    for cell in annotations.get('cells', []):
        lloc = cell.get('lloc', {})
        for r in range(lloc.get('start_row', 0), lloc.get('end_row', 0) + 1):
            for c in range(lloc.get('start_col', 0), lloc.get('end_col', 0) + 1):
                actual_positions.add((r, c))

    return len(actual_positions) / expected_cells if expected_cells > 0 else 0


def validate_table_sample(annotation_file, metadata_file):
    """
    验证单个表格样本 - 简化版本，只包含最核心的检查
    """
    issues = []
    score = 100.0

    try:
        with open(annotation_file, 'r', encoding='utf-8') as f:
            annotations = json.load(f)
        with open(metadata_file, 'r', encoding='utf-8') as f:
            metadata = json.load(f)

        # 检查1: 坐标宽度一致性 (核心问题)
        coord_issues = check_coordinate_consistency(annotations)
        if coord_issues:
            score -= 50
            issues.extend(coord_issues)

        # 检查2: 基础数据完整性
        cells = annotations.get('cells', [])
        if not cells:
            score -= 30
            issues.append("缺少有效的单元格数据")
        else:
            # 检查单元格数据完整性
            invalid_cells = []
            for i, cell in enumerate(cells):
                cell_issues = []
                if 'bbox' not in cell or not cell['bbox']:
                    cell_issues.append("缺少bbox")
                if 'lloc' not in cell or not cell['lloc']:
                    cell_issues.append("缺少lloc")

                if cell_issues:
                    lloc = cell.get('lloc', {})
                    row_pos = lloc.get('start_row', -1)
                    col_pos = lloc.get('start_col', -1)
                    content = cell.get('content', '')[:10]

                    cell_desc = f"第{row_pos}行第{col_pos}列"
                    if content:
                        cell_desc += f"('{content}')"

                    invalid_cells.append(f"{cell_desc}: {', '.join(cell_issues)}")

            if invalid_cells:
                score -= 20
                issues.append(f"数据不完整的单元格: {'; '.join(invalid_cells[:3])}")  # 只显示前3个
                if len(invalid_cells) > 3:
                    issues.append(f"...还有{len(invalid_cells)-3}个单元格有类似问题")

        # 检查3: 表格覆盖率
        coverage_ratio = calculate_coverage_ratio(annotations, metadata)
        if coverage_ratio < 0.7:
            score -= 20
            expected_rows = metadata.get('rows', 0)
            expected_cols = metadata.get('cols', 0)
            issues.append(f"表格覆盖率过低: {coverage_ratio:.1%} (预期{expected_rows}行{expected_cols}列)")

        return score >= 70, issues, score

    except Exception as e:
        return False, [f"文件处理错误: {str(e)}"], 0.0


def main():
    """主函数"""
    parser = argparse.ArgumentParser(
        description='清理TableRender生成的无效数据并验证表格结构质量',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
示例用法:
  python clean_invalid_data.py --input_dir ./generated_data
  python clean_invalid_data.py --input_dir ./generated_data --dry-run
  python clean_invalid_data.py --input_dir ./generated_data --verbose

功能说明:
  1. 清理图像尺寸超出预期的无效样本
  2. 验证表格样本的结构质量（坐标一致性、覆盖率等）
  3. 生成无效表格样本报告 (invalid_table_samples.json)
        """
    )
    
    parser.add_argument(
        '--input_dir',
        type=str,
        required=True,
        help='生成数据的根目录路径'
    )
    
    parser.add_argument(
        '--dry-run',
        action='store_true',
        help='干运行模式，只显示会被删除的文件而不实际删除'
    )
    
    parser.add_argument(
        '--verbose',
        action='store_true',
        help='启用详细日志输出'
    )
    
    args = parser.parse_args()
    
    # 设置日志级别
    setup_logging(args.verbose)
    
    try:
        logger.info("开始数据清理和验证...")
        if args.dry_run:
            logger.info("运行模式: DRY RUN (不会实际删除文件)")

        # === 现有清理逻辑 (保持不变) ===
        logger.info("执行现有数据清理...")
        clean_invalid_data(args.input_dir, args.dry_run)

        # === 新增：表格结构验证 ===
        logger.info("执行表格结构验证...")
        table_pairs = find_table_sample_pairs(args.input_dir)

        if table_pairs:
            logger.info(f"发现 {len(table_pairs)} 个表格样本")
            invalid_table_samples = []
            quality_scores = []

            for ann_file, meta_file in tqdm(table_pairs, desc="验证表格样本"):
                is_valid, issues, score = validate_table_sample(ann_file, meta_file)
                quality_scores.append(score)

                if not is_valid:
                    invalid_table_samples.append({
                        'annotation_file': ann_file,
                        'metadata_file': meta_file,
                        'issues': issues,
                        'score': score
                    })

            # 输出表格验证结果
            valid_count = len(table_pairs) - len(invalid_table_samples)
            logger.info(f"表格验证完成: {valid_count}/{len(table_pairs)} 有效")
            if quality_scores:
                logger.info(f"平均质量得分: {np.mean(quality_scores):.1f}")

            if invalid_table_samples:
                invalid_table_file = os.path.join(args.input_dir, "invalid_table_samples.json")
                with open(invalid_table_file, 'w', encoding='utf-8') as f:
                    json.dump(invalid_table_samples, f, indent=2, ensure_ascii=False)
                logger.info(f"无效表格样本列表已保存: {invalid_table_file}")
        else:
            logger.info("未发现表格样本，跳过表格结构验证")

        logger.info("数据清理和验证完成!")

    except Exception as e:
        logger.error(f"处理过程中发生错误: {str(e)}")
        return 1
    
    return 0


if __name__ == '__main__':
    exit(main())
