{"sample_seed": 174394718, "sample_index": 52, "generation_timestamp": 1754529519.378867, "turbo_jpeg_used": true, "structure_choices": {"header_rows": {"value": 3, "range": [3, 3], "probability": 0.1}, "body_rows": {"value": 26, "range": [21, 30], "probability": 0.1}, "cols": {"value": 5, "range": [2, 5], "probability": 0.6}, "merge_probability": {"value": 0.07975153793348871, "range": [0, 0.1], "probability": 0.5}, "max_row_span": {"value": 8, "probability": 0.2}, "max_col_span": {"value": 8, "probability": 0.2}}, "style_choices": {"font_family": {"value": "Times New Roman", "probability": 0.25}, "font_size": {"value": 22, "range": [21, 30], "probability": 0.1}, "horizontal_align": {"value": "center", "probability": 0.6}, "vertical_align": {"value": "middle", "probability": 0.6}, "padding": {"value": 4, "range": [4, 6], "probability": 0.4}}, "file_choices": {"csv": {"file": "assets/corpus/nl2sql_train/0021rows_batch_001.csv", "directory": "assets/corpus/nl2sql_train/", "probability": 0.5}, "font": {"file": "/aipdf-mlp/jiacheng/code/text_render/example_data/font/common/times_new_roman.ttf", "directory": "/aipdf-mlp/jiacheng/code/text_render/example_data/font/common", "probability": 0.8}, "background": {"file": "/aipdf-mlp/jiacheng/code/text_render/example_data/bg/bg_resource/pure_white/classic_white_doc_bg.jpg", "directory": "/aipdf-mlp/jiacheng/code/text_render/example_data/bg/bg_resource/pure_white", "probability": 0.5}}, "postprocessing_choices": {"margin_control": {"value": 150.0, "range": [100, 120], "probability": 0.2}, "degradation_effects_applied": [], "degradation_status": {"blur_applied": false, "noise_applied": false, "fade_global_applied": false, "fade_local_applied": false, "uneven_lighting_applied": false, "jpeg_applied": false, "darker_brighter_applied": false, "gamma_correction_applied": false}, "perspective_applied": false, "background_applied": true, "table_blending_enabled": true, "transparency_settings": {"default_color_transparency": 0.0, "meaningful_color_transparency": 0.7}}, "performance_info": {"parallel_enabled": true, "max_workers": 16, "turbo_jpeg_enabled": true, "turbo_jpeg_quality": 100, "turbo_jpeg_format": "png", "css_stability_enabled": true}, "inheritance_applied": {"font_family_changed": false, "font_size_changed": false, "alignment_changed": false, "padding_changed": false, "text_color_changed": false, "background_color_changed": false}, "css_render_info": {"table_position": {"left": 3932, "top": 289}, "background_dimensions": {"width": 9600.0, "height": 2160.0}, "crop_dimensions": {"width": 9600.0, "height": 2160.0}, "background_offset": {"x": 0, "y": 0}, "scaling_factors": {"max_scale_factor": 5.0, "content_area_shrink_ratio": 0.1}}, "csv_sampling_info": {"source_file": "assets/corpus/nl2sql_train/0021rows_batch_001.csv", "selected_columns": [106, 6, 122, 149, 94], "selected_rows": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 17, 3, 10, 8, 9, 5], "csv_structure": {"total_columns": 164, "total_data_rows": 20}, "sampling_mode": "random"}, "resolved_key_params": {"table_size": {"header_rows": 3, "body_rows": 26, "cols": 5}, "content_source": "csv", "overflow_strategy": "wrap", "border_mode": "none", "zebra_stripes_enabled": true, "transparency_enabled": false, "random_seed": 174394718}, "color_diff_info": {"statistics": {"total_borders_checked": 375, "color_diff_triggered": 0, "header_separator_affected": 0, "max_color_difference": 34, "min_color_difference": 0}, "triggered_borders": []}}