{"sample_seed": 2329631631, "sample_index": 5, "generation_timestamp": 1754500009.374398, "turbo_jpeg_used": true, "structure_choices": {"header_rows": {"value": 1, "range": [1, 1], "probability": 0.6}, "body_rows": {"value": 19, "range": [11, 20], "probability": 0.4}, "cols": {"value": 6, "range": [6, 10], "probability": 0.3}, "merge_probability": {"value": 0.03604180369424944, "range": [0, 0.1], "probability": 0.5}, "max_row_span": {"value": 5, "probability": 0.6}, "max_col_span": {"value": 8, "probability": 0.2}}, "style_choices": {"font_family": {"value": "<PERSON><PERSON>", "probability": 0.4}, "font_size": {"value": 11, "range": [10, 15], "probability": 0.4}, "horizontal_align": {"value": "center", "probability": 0.6}, "vertical_align": {"value": "top", "probability": 0.2}, "padding": {"value": 9, "range": [7, 10], "probability": 0.3}}, "file_choices": {"csv": {"file": "assets/corpus/wikisql_train/0007rows_batch_083.csv", "directory": "assets/corpus/wikisql_train/", "probability": 0.5}, "font": {"file": "/aipdf-mlp/jiacheng/code/text_render/example_data/font/common/arial.ttf", "directory": "/aipdf-mlp/jiacheng/code/text_render/example_data/font/common", "probability": 0.8}, "background": {"file": "/aicamera-mlp/yangyunfei/PrintProject/YFData/dataset/beauty_template/Delight_dae/task_color/639_in.jpg", "directory": "/aicamera-mlp/yangyunfei/PrintProject/YFData/dataset/beauty_template/Delight_dae/task_color", "probability": 0.01}}, "postprocessing_choices": {"margin_control": {"value": 93, "range": [80, 100], "probability": 0.3}, "degradation_effects_applied": ["jpeg", "darker_brighter"], "degradation_effects_config": {"jpeg_probability": 0.05, "darker_brighter_probability": 0.05}, "degradation_status": {"blur_applied": false, "noise_applied": false, "fade_global_applied": false, "fade_local_applied": false, "uneven_lighting_applied": false, "jpeg_applied": true, "darker_brighter_applied": true, "gamma_correction_applied": false}, "perspective_applied": false, "background_applied": true, "table_blending_enabled": true, "transparency_settings": {"default_color_transparency": 0.0, "meaningful_color_transparency": 1.0}}, "performance_info": {"parallel_enabled": true, "max_workers": 16, "turbo_jpeg_enabled": true, "turbo_jpeg_quality": 100, "turbo_jpeg_format": "png", "css_stability_enabled": true}, "inheritance_applied": {"font_family_changed": false, "font_size_changed": false, "alignment_changed": false, "padding_changed": false, "text_color_changed": false, "background_color_changed": false}, "css_render_info": {"table_position": {"left": 5488, "top": 608}, "background_dimensions": {"width": 12390.0, "height": 2782.0}, "crop_dimensions": {"width": 12390.0, "height": 2782.0}, "background_offset": {"x": 0, "y": 0}, "scaling_factors": {"max_scale_factor": 5.0, "content_area_shrink_ratio": 0.1}}, "csv_sampling_info": {"source_file": "assets/corpus/wikisql_train/0007rows_batch_083.csv", "selected_columns": [58, 90, 40, 72, 24, 129], "selected_rows": [0, 1, 2, 3, 4, 5, 2, 0, 5, 3, 5, 0, 2, 1, 3, 2, 3, 3, 5], "csv_structure": {"total_columns": 130, "total_data_rows": 6}, "sampling_mode": "random"}, "resolved_key_params": {"table_size": {"header_rows": 1, "body_rows": 19, "cols": 6}, "content_source": "csv", "overflow_strategy": "wrap", "border_mode": "none", "zebra_stripes_enabled": true, "transparency_enabled": false, "random_seed": 2329631631}, "color_diff_info": {"statistics": {"total_borders_checked": 348, "color_diff_triggered": 0, "header_separator_affected": 0, "max_color_difference": 57, "min_color_difference": 0}, "triggered_borders": [], "border_adjustments": [{"cell_id": "cell-1-0", "cell_position": [1, 0], "direction": "left", "original_border": 0, "final_border": 1, "adjustment_type": "color_diff_or_logic", "color_diff_triggered": true}, {"cell_id": "cell-1-5", "cell_position": [1, 5], "direction": "right", "original_border": 0, "final_border": 1, "adjustment_type": "color_diff_or_logic", "color_diff_triggered": true}, {"cell_id": "cell-2-0", "cell_position": [2, 0], "direction": "left", "original_border": 0, "final_border": 1, "adjustment_type": "color_diff_or_logic", "color_diff_triggered": true}, {"cell_id": "cell-2-5", "cell_position": [2, 5], "direction": "right", "original_border": 0, "final_border": 1, "adjustment_type": "color_diff_or_logic", "color_diff_triggered": true}, {"cell_id": "cell-3-0", "cell_position": [3, 0], "direction": "left", "original_border": 0, "final_border": 1, "adjustment_type": "color_diff_or_logic", "color_diff_triggered": true}, {"cell_id": "cell-3-5", "cell_position": [3, 5], "direction": "right", "original_border": 0, "final_border": 1, "adjustment_type": "color_diff_or_logic", "color_diff_triggered": true}, {"cell_id": "cell-4-0", "cell_position": [4, 0], "direction": "left", "original_border": 0, "final_border": 1, "adjustment_type": "color_diff_or_logic", "color_diff_triggered": true}, {"cell_id": "cell-4-5", "cell_position": [4, 5], "direction": "right", "original_border": 0, "final_border": 1, "adjustment_type": "color_diff_or_logic", "color_diff_triggered": true}, {"cell_id": "cell-5-0", "cell_position": [5, 0], "direction": "left", "original_border": 0, "final_border": 1, "adjustment_type": "color_diff_or_logic", "color_diff_triggered": true}, {"cell_id": "cell-5-5", "cell_position": [5, 5], "direction": "right", "original_border": 0, "final_border": 1, "adjustment_type": "color_diff_or_logic", "color_diff_triggered": true}, {"cell_id": "cell-6-0", "cell_position": [6, 0], "direction": "left", "original_border": 0, "final_border": 1, "adjustment_type": "color_diff_or_logic", "color_diff_triggered": true}, {"cell_id": "cell-6-5", "cell_position": [6, 5], "direction": "right", "original_border": 0, "final_border": 1, "adjustment_type": "color_diff_or_logic", "color_diff_triggered": true}, {"cell_id": "cell-7-0", "cell_position": [7, 0], "direction": "left", "original_border": 0, "final_border": 1, "adjustment_type": "color_diff_or_logic", "color_diff_triggered": true}, {"cell_id": "cell-7-5", "cell_position": [7, 5], "direction": "right", "original_border": 0, "final_border": 1, "adjustment_type": "color_diff_or_logic", "color_diff_triggered": true}, {"cell_id": "cell-8-0", "cell_position": [8, 0], "direction": "left", "original_border": 0, "final_border": 1, "adjustment_type": "color_diff_or_logic", "color_diff_triggered": true}, {"cell_id": "cell-8-5", "cell_position": [8, 5], "direction": "right", "original_border": 0, "final_border": 1, "adjustment_type": "color_diff_or_logic", "color_diff_triggered": true}, {"cell_id": "cell-9-0", "cell_position": [9, 0], "direction": "right", "original_border": 0, "final_border": 1, "adjustment_type": "color_diff_or_logic", "color_diff_triggered": true}, {"cell_id": "cell-9-0", "cell_position": [9, 0], "direction": "left", "original_border": 0, "final_border": 1, "adjustment_type": "color_diff_or_logic", "color_diff_triggered": true}, {"cell_id": "cell-12-0", "cell_position": [12, 0], "direction": "left", "original_border": 0, "final_border": 1, "adjustment_type": "color_diff_or_logic", "color_diff_triggered": true}, {"cell_id": "cell-12-5", "cell_position": [12, 5], "direction": "right", "original_border": 0, "final_border": 1, "adjustment_type": "color_diff_or_logic", "color_diff_triggered": true}, {"cell_id": "cell-13-0", "cell_position": [13, 0], "direction": "left", "original_border": 0, "final_border": 1, "adjustment_type": "color_diff_or_logic", "color_diff_triggered": true}, {"cell_id": "cell-13-5", "cell_position": [13, 5], "direction": "right", "original_border": 0, "final_border": 1, "adjustment_type": "color_diff_or_logic", "color_diff_triggered": true}, {"cell_id": "cell-14-0", "cell_position": [14, 0], "direction": "left", "original_border": 0, "final_border": 1, "adjustment_type": "color_diff_or_logic", "color_diff_triggered": true}, {"cell_id": "cell-14-5", "cell_position": [14, 5], "direction": "right", "original_border": 0, "final_border": 1, "adjustment_type": "color_diff_or_logic", "color_diff_triggered": true}, {"cell_id": "cell-15-0", "cell_position": [15, 0], "direction": "left", "original_border": 0, "final_border": 1, "adjustment_type": "color_diff_or_logic", "color_diff_triggered": true}, {"cell_id": "cell-15-5", "cell_position": [15, 5], "direction": "right", "original_border": 0, "final_border": 1, "adjustment_type": "color_diff_or_logic", "color_diff_triggered": true}, {"cell_id": "cell-16-0", "cell_position": [16, 0], "direction": "left", "original_border": 0, "final_border": 1, "adjustment_type": "color_diff_or_logic", "color_diff_triggered": true}, {"cell_id": "cell-16-5", "cell_position": [16, 5], "direction": "right", "original_border": 0, "final_border": 1, "adjustment_type": "color_diff_or_logic", "color_diff_triggered": true}, {"cell_id": "cell-17-0", "cell_position": [17, 0], "direction": "left", "original_border": 0, "final_border": 1, "adjustment_type": "color_diff_or_logic", "color_diff_triggered": true}, {"cell_id": "cell-17-5", "cell_position": [17, 5], "direction": "right", "original_border": 0, "final_border": 1, "adjustment_type": "color_diff_or_logic", "color_diff_triggered": true}, {"cell_id": "cell-18-0", "cell_position": [18, 0], "direction": "left", "original_border": 0, "final_border": 1, "adjustment_type": "color_diff_or_logic", "color_diff_triggered": true}, {"cell_id": "cell-18-5", "cell_position": [18, 5], "direction": "right", "original_border": 0, "final_border": 1, "adjustment_type": "color_diff_or_logic", "color_diff_triggered": true}, {"cell_id": "cell-19-0", "cell_position": [19, 0], "direction": "bottom", "original_border": 0, "final_border": 1, "adjustment_type": "color_diff_or_logic", "color_diff_triggered": true}, {"cell_id": "cell-19-0", "cell_position": [19, 0], "direction": "left", "original_border": 0, "final_border": 1, "adjustment_type": "color_diff_or_logic", "color_diff_triggered": true}, {"cell_id": "cell-19-1", "cell_position": [19, 1], "direction": "bottom", "original_border": 0, "final_border": 1, "adjustment_type": "color_diff_or_logic", "color_diff_triggered": true}, {"cell_id": "cell-19-2", "cell_position": [19, 2], "direction": "bottom", "original_border": 0, "final_border": 1, "adjustment_type": "color_diff_or_logic", "color_diff_triggered": true}, {"cell_id": "cell-19-3", "cell_position": [19, 3], "direction": "bottom", "original_border": 0, "final_border": 1, "adjustment_type": "color_diff_or_logic", "color_diff_triggered": true}, {"cell_id": "cell-19-4", "cell_position": [19, 4], "direction": "bottom", "original_border": 0, "final_border": 1, "adjustment_type": "color_diff_or_logic", "color_diff_triggered": true}, {"cell_id": "cell-19-5", "cell_position": [19, 5], "direction": "right", "original_border": 0, "final_border": 1, "adjustment_type": "color_diff_or_logic", "color_diff_triggered": true}, {"cell_id": "cell-19-5", "cell_position": [19, 5], "direction": "bottom", "original_border": 0, "final_border": 1, "adjustment_type": "color_diff_or_logic", "color_diff_triggered": true}], "adjustment_summary": {"total_adjustments": 40, "header_separator_adjustments": 0, "or_logic_adjustments": 40, "affected_cells": ["cell-2-0", "cell-15-0", "cell-12-0", "cell-15-5", "cell-5-5", "cell-16-0", "cell-8-0", "cell-19-4", "cell-1-5", "cell-13-0", "cell-17-0", "cell-18-5", "cell-3-0", "cell-19-5", "cell-9-0", "cell-3-5", "cell-12-5", "cell-19-0", "cell-19-1", "cell-4-0", "cell-19-2", "cell-4-5", "cell-14-5", "cell-16-5", "cell-17-5", "cell-18-0", "cell-13-5", "cell-7-5", "cell-7-0", "cell-2-5", "cell-1-0", "cell-14-0", "cell-19-3", "cell-6-5", "cell-6-0", "cell-8-5", "cell-5-0"]}}}