{"sample_seed": 1364834674, "sample_index": 2, "generation_timestamp": 1754493679.6519675, "turbo_jpeg_used": true, "structure_choices": {"header_rows": {"value": 2, "range": [2, 2], "probability": 0.3}, "body_rows": {"value": 8, "range": [6, 10], "probability": 0.3}, "cols": {"value": 3, "range": [2, 5], "probability": 0.6}, "merge_probability": {"value": 0.016858945902709234, "range": [0, 0.1], "probability": 0.5}, "max_row_span": {"value": 2, "probability": 0.2}, "max_col_span": {"value": 8, "probability": 0.2}}, "style_choices": {"font_family": {"value": "Calib<PERSON>", "probability": 0.1}, "font_size": {"value": 19, "range": [16, 20], "probability": 0.5}, "horizontal_align": {"value": "center", "probability": 0.6}, "vertical_align": {"value": "top", "probability": 0.2}, "padding": {"value": 8, "range": [7, 10], "probability": 0.3}}, "file_choices": {"csv": {"file": "assets/corpus/wikisql_train/0006rows_batch_018.csv", "directory": "assets/corpus/wikisql_train/", "probability": 0.5}, "font": {"file": "/aipdf-mlp/jiacheng/code/text_render/example_data/font/common/calibri.ttf", "directory": "/aipdf-mlp/jiacheng/code/text_render/example_data/font/common", "probability": 0.8}, "background": {"file": "/aipdf-mlp/jiacheng/code/text_render/example_data/bg/bg_resource/paper/paper_00075.jpg", "directory": "/aipdf-mlp/jiacheng/code/text_render/example_data/bg/bg_resource/paper", "probability": 0.1}}, "postprocessing_choices": {"margin_control": {"value": 74, "range": [60, 80], "probability": 0.3}, "degradation_effects_applied": [], "degradation_status": {"blur_applied": false, "noise_applied": false, "fade_global_applied": false, "fade_local_applied": false, "uneven_lighting_applied": false, "jpeg_applied": false, "darker_brighter_applied": false, "gamma_correction_applied": false}, "perspective_applied": false, "background_applied": true, "table_blending_enabled": true, "transparency_settings": {"default_color_transparency": 0.0, "meaningful_color_transparency": 0.7}}, "performance_info": {"parallel_enabled": true, "max_workers": 16, "turbo_jpeg_enabled": true, "turbo_jpeg_quality": 100, "turbo_jpeg_format": "png", "css_stability_enabled": true}, "inheritance_applied": {"font_family_changed": false, "font_size_changed": false, "alignment_changed": false, "padding_changed": false, "text_color_changed": false, "background_color_changed": false}, "css_render_info": {"table_position": {"left": 1398, "top": 20}, "background_dimensions": {"width": 3400.0, "height": 908.0}, "crop_dimensions": {"width": 3400.0, "height": 908.0}, "background_offset": {"x": 0, "y": 0}, "scaling_factors": {"max_scale_factor": 5.0, "content_area_shrink_ratio": 0.1}}, "csv_sampling_info": {"source_file": "assets/corpus/wikisql_train/0006rows_batch_018.csv", "selected_columns": [118, 126, 83], "selected_rows": [0, 1, 2, 3, 4, 3, 0, 2], "csv_structure": {"total_columns": 139, "total_data_rows": 5}, "sampling_mode": "random"}, "resolved_key_params": {"table_size": {"header_rows": 2, "body_rows": 8, "cols": 3}, "content_source": "csv", "overflow_strategy": "wrap", "border_mode": "none", "zebra_stripes_enabled": true, "transparency_enabled": false, "random_seed": 1364834674}, "color_diff_info": {"statistics": {"total_borders_checked": 94, "color_diff_triggered": 6, "header_separator_affected": 0, "max_color_difference": 48, "min_color_difference": 48}, "triggered_borders": [{"cell_id": "cell-1-0", "cell_position": [1, 0], "direction": "bottom", "cell_color": "#FFFFFF", "adjacent_cell_id": "cell-2-0", "adjacent_cell_position": [2, 0], "adjacent_color": "#ECF0F1", "color_difference": 48, "is_header_separator": false, "trigger_reason": "color_difference"}, {"cell_id": "cell-1-1", "cell_position": [1, 1], "direction": "bottom", "cell_color": "#FFFFFF", "adjacent_cell_id": "cell-2-1", "adjacent_cell_position": [2, 1], "adjacent_color": "#ECF0F1", "color_difference": 48, "is_header_separator": false, "trigger_reason": "color_difference"}, {"cell_id": "cell-1-2", "cell_position": [1, 2], "direction": "bottom", "cell_color": "#FFFFFF", "adjacent_cell_id": "cell-2-2", "adjacent_cell_position": [2, 2], "adjacent_color": "#ECF0F1", "color_difference": 48, "is_header_separator": false, "trigger_reason": "color_difference"}, {"cell_id": "cell-2-0", "cell_position": [2, 0], "direction": "top", "cell_color": "#ECF0F1", "adjacent_cell_id": "cell-1-0", "adjacent_cell_position": [1, 0], "adjacent_color": "#FFFFFF", "color_difference": 48, "is_header_separator": false, "trigger_reason": "color_difference"}, {"cell_id": "cell-2-1", "cell_position": [2, 1], "direction": "top", "cell_color": "#ECF0F1", "adjacent_cell_id": "cell-1-1", "adjacent_cell_position": [1, 1], "adjacent_color": "#FFFFFF", "color_difference": 48, "is_header_separator": false, "trigger_reason": "color_difference"}, {"cell_id": "cell-2-2", "cell_position": [2, 2], "direction": "top", "cell_color": "#ECF0F1", "adjacent_cell_id": "cell-1-2", "adjacent_cell_position": [1, 2], "adjacent_color": "#FFFFFF", "color_difference": 48, "is_header_separator": false, "trigger_reason": "color_difference"}], "summary": {"total_triggered": 6, "header_separator_count": 0, "regular_border_count": 6, "max_color_difference": 48, "min_color_difference": 48, "affected_cells": ["cell-1-2", "cell-2-1", "cell-1-1", "cell-1-0", "cell-2-0", "cell-2-2"]}, "border_adjustments": [{"cell_id": "cell-1-0", "cell_position": [1, 0], "direction": "bottom", "original_border": 0, "final_border": 1, "adjustment_type": "color_diff_or_logic", "color_diff_triggered": true}, {"cell_id": "cell-1-1", "cell_position": [1, 1], "direction": "bottom", "original_border": 0, "final_border": 1, "adjustment_type": "color_diff_or_logic", "color_diff_triggered": true}, {"cell_id": "cell-1-2", "cell_position": [1, 2], "direction": "bottom", "original_border": 0, "final_border": 1, "adjustment_type": "color_diff_or_logic", "color_diff_triggered": true}, {"cell_id": "cell-2-0", "cell_position": [2, 0], "direction": "top", "original_border": 0, "final_border": 1, "adjustment_type": "color_diff_or_logic", "color_diff_triggered": true}, {"cell_id": "cell-2-0", "cell_position": [2, 0], "direction": "left", "original_border": 0, "final_border": 1, "adjustment_type": "color_diff_or_logic", "color_diff_triggered": true}, {"cell_id": "cell-2-1", "cell_position": [2, 1], "direction": "top", "original_border": 0, "final_border": 1, "adjustment_type": "color_diff_or_logic", "color_diff_triggered": true}, {"cell_id": "cell-2-2", "cell_position": [2, 2], "direction": "top", "original_border": 0, "final_border": 1, "adjustment_type": "color_diff_or_logic", "color_diff_triggered": true}, {"cell_id": "cell-2-2", "cell_position": [2, 2], "direction": "right", "original_border": 0, "final_border": 1, "adjustment_type": "color_diff_or_logic", "color_diff_triggered": true}, {"cell_id": "cell-3-0", "cell_position": [3, 0], "direction": "left", "original_border": 0, "final_border": 1, "adjustment_type": "color_diff_or_logic", "color_diff_triggered": true}, {"cell_id": "cell-3-2", "cell_position": [3, 2], "direction": "right", "original_border": 0, "final_border": 1, "adjustment_type": "color_diff_or_logic", "color_diff_triggered": true}, {"cell_id": "cell-4-0", "cell_position": [4, 0], "direction": "left", "original_border": 0, "final_border": 1, "adjustment_type": "color_diff_or_logic", "color_diff_triggered": true}, {"cell_id": "cell-4-2", "cell_position": [4, 2], "direction": "right", "original_border": 0, "final_border": 1, "adjustment_type": "color_diff_or_logic", "color_diff_triggered": true}, {"cell_id": "cell-5-0", "cell_position": [5, 0], "direction": "left", "original_border": 0, "final_border": 1, "adjustment_type": "color_diff_or_logic", "color_diff_triggered": true}, {"cell_id": "cell-5-2", "cell_position": [5, 2], "direction": "right", "original_border": 0, "final_border": 1, "adjustment_type": "color_diff_or_logic", "color_diff_triggered": true}, {"cell_id": "cell-6-0", "cell_position": [6, 0], "direction": "left", "original_border": 0, "final_border": 1, "adjustment_type": "color_diff_or_logic", "color_diff_triggered": true}, {"cell_id": "cell-6-2", "cell_position": [6, 2], "direction": "right", "original_border": 0, "final_border": 1, "adjustment_type": "color_diff_or_logic", "color_diff_triggered": true}, {"cell_id": "cell-7-0", "cell_position": [7, 0], "direction": "left", "original_border": 0, "final_border": 1, "adjustment_type": "color_diff_or_logic", "color_diff_triggered": true}, {"cell_id": "cell-7-2", "cell_position": [7, 2], "direction": "right", "original_border": 0, "final_border": 1, "adjustment_type": "color_diff_or_logic", "color_diff_triggered": true}, {"cell_id": "cell-8-0", "cell_position": [8, 0], "direction": "left", "original_border": 0, "final_border": 1, "adjustment_type": "color_diff_or_logic", "color_diff_triggered": true}, {"cell_id": "cell-8-2", "cell_position": [8, 2], "direction": "right", "original_border": 0, "final_border": 1, "adjustment_type": "color_diff_or_logic", "color_diff_triggered": true}, {"cell_id": "cell-9-0", "cell_position": [9, 0], "direction": "bottom", "original_border": 0, "final_border": 1, "adjustment_type": "color_diff_or_logic", "color_diff_triggered": true}, {"cell_id": "cell-9-0", "cell_position": [9, 0], "direction": "left", "original_border": 0, "final_border": 1, "adjustment_type": "color_diff_or_logic", "color_diff_triggered": true}, {"cell_id": "cell-9-1", "cell_position": [9, 1], "direction": "bottom", "original_border": 0, "final_border": 1, "adjustment_type": "color_diff_or_logic", "color_diff_triggered": true}, {"cell_id": "cell-9-2", "cell_position": [9, 2], "direction": "right", "original_border": 0, "final_border": 1, "adjustment_type": "color_diff_or_logic", "color_diff_triggered": true}, {"cell_id": "cell-9-2", "cell_position": [9, 2], "direction": "bottom", "original_border": 0, "final_border": 1, "adjustment_type": "color_diff_or_logic", "color_diff_triggered": true}], "adjustment_summary": {"total_adjustments": 25, "header_separator_adjustments": 0, "or_logic_adjustments": 25, "affected_cells": ["cell-1-2", "cell-3-2", "cell-1-1", "cell-4-2", "cell-8-0", "cell-7-2", "cell-6-0", "cell-3-0", "cell-6-2", "cell-5-0", "cell-9-0", "cell-7-0", "cell-4-0", "cell-2-2", "cell-9-1", "cell-9-2", "cell-2-1", "cell-1-0", "cell-8-2", "cell-5-2", "cell-2-0"]}}}