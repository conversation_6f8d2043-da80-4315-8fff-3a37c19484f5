{"sample_seed": 2647887792, "sample_index": 68, "generation_timestamp": 1754527801.9476569, "turbo_jpeg_used": true, "structure_choices": {"header_rows": {"value": 3, "range": [3, 3], "probability": 0.1}, "body_rows": {"value": 4, "range": [2, 5], "probability": 0.2}, "cols": {"value": 5, "range": [2, 5], "probability": 0.6}, "merge_probability": {"value": 0.1656805754183086, "range": [0.15, 0.2], "probability": 0.2}, "max_row_span": {"value": 8, "probability": 0.2}, "max_col_span": {"value": 2, "probability": 0.2}}, "style_choices": {"font_family": {"value": "<PERSON><PERSON>", "probability": 0.4}, "font_size": {"value": 11, "range": [10, 15], "probability": 0.4}, "horizontal_align": {"value": "center", "probability": 0.6}, "vertical_align": {"value": "middle", "probability": 0.6}, "padding": {"value": 1, "range": [1, 3], "probability": 0.3}}, "file_choices": {"csv": {"file": "assets/corpus/wikisql_train/0116rows_batch_001.csv", "directory": "assets/corpus/wikisql_train/", "probability": 0.5}, "font": {"file": "/aipdf-mlp/jiacheng/code/text_render/example_data/font/rare/arial.ttf", "directory": "/aipdf-mlp/jiacheng/code/text_render/example_data/font/rare", "probability": 0.2}, "background": {"file": "/aipdf-mlp/jiacheng/code/text_render/example_data/bg/bg_resource/pure_white/classic_white_doc_bg.jpg", "directory": "/aipdf-mlp/jiacheng/code/text_render/example_data/bg/bg_resource/pure_white", "probability": 0.5}}, "postprocessing_choices": {"margin_control": {"value": 60, "range": [30, 60], "probability": 0.2}, "degradation_effects_applied": ["jpeg"], "degradation_effects_config": {"jpeg_probability": 0.05}, "degradation_status": {"blur_applied": false, "noise_applied": false, "fade_global_applied": false, "fade_local_applied": false, "uneven_lighting_applied": false, "jpeg_applied": true, "darker_brighter_applied": false, "gamma_correction_applied": false}, "perspective_applied": false, "background_applied": true, "table_blending_enabled": true, "transparency_settings": {"default_color_transparency": 0.0, "meaningful_color_transparency": 0.7}}, "performance_info": {"parallel_enabled": true, "max_workers": 16, "turbo_jpeg_enabled": true, "turbo_jpeg_quality": 100, "turbo_jpeg_format": "png", "css_stability_enabled": true}, "inheritance_applied": {"font_family_changed": false, "font_size_changed": false, "alignment_changed": false, "padding_changed": false, "text_color_changed": false, "background_color_changed": false}, "css_render_info": {"table_position": {"left": 3943, "top": 209}, "background_dimensions": {"width": 9600.0, "height": 2160.0}, "crop_dimensions": {"width": 9600.0, "height": 2160.0}, "background_offset": {"x": 0, "y": 0}, "scaling_factors": {"max_scale_factor": 5.0, "content_area_shrink_ratio": 0.1}}, "csv_sampling_info": {"source_file": "assets/corpus/wikisql_train/0116rows_batch_001.csv", "selected_columns": [6, 10, 8, 19, 9], "selected_rows": [83, 40, 105, 75], "csv_structure": {"total_columns": 23, "total_data_rows": 115}, "sampling_mode": "random"}, "resolved_key_params": {"table_size": {"header_rows": 3, "body_rows": 4, "cols": 5}, "content_source": "csv", "overflow_strategy": "wrap", "border_mode": "none", "zebra_stripes_enabled": true, "transparency_enabled": false, "random_seed": 2647887792}, "color_diff_info": {"statistics": {"total_borders_checked": 93, "color_diff_triggered": 0, "header_separator_affected": 0, "max_color_difference": 34, "min_color_difference": 0}, "triggered_borders": []}}