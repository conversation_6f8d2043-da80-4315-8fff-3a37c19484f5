{"sample_seed": 2898690414, "sample_index": 94, "generation_timestamp": 1754487689.021645, "turbo_jpeg_used": true, "structure_choices": {"header_rows": {"value": 1, "range": [1, 1], "probability": 0.6}, "body_rows": {"value": 5, "range": [2, 5], "probability": 0.2}, "cols": {"value": 4, "range": [2, 5], "probability": 0.6}, "merge_probability": {"value": 0.15083187424233738, "range": [0.15, 0.2], "probability": 0.2}, "max_row_span": {"value": 8, "probability": 0.2}, "max_col_span": {"value": 2, "probability": 0.2}}, "style_choices": {"font_family": {"value": "Calib<PERSON>", "probability": 0.1}, "font_size": {"value": 13, "range": [10, 15], "probability": 0.4}, "horizontal_align": {"value": "center", "probability": 0.6}, "vertical_align": {"value": "middle", "probability": 0.6}, "padding": {"value": 3, "range": [1, 3], "probability": 0.3}}, "file_choices": {"csv": {"file": "assets/corpus/nl2sql_train/0011rows_batch_004.csv", "directory": "assets/corpus/nl2sql_train/", "probability": 0.5}, "font": {"file": "/aipdf-mlp/jiacheng/code/text_render/example_data/font/common/calibri.ttf", "directory": "/aipdf-mlp/jiacheng/code/text_render/example_data/font/common", "probability": 0.8}, "background": {"file": "/aipdf-mlp/jiacheng/code/text_render/example_data/bg/bg_resource/pure_white/classic_white_doc_bg.jpg", "directory": "/aipdf-mlp/jiacheng/code/text_render/example_data/bg/bg_resource/pure_white", "probability": 0.5}}, "postprocessing_choices": {"margin_control": {"value": 45, "range": [30, 60], "probability": 0.2}, "degradation_effects_applied": [], "degradation_status": {"blur_applied": false, "noise_applied": false, "fade_global_applied": false, "fade_local_applied": false, "uneven_lighting_applied": false, "jpeg_applied": false, "darker_brighter_applied": false, "gamma_correction_applied": false}, "perspective_applied": false, "background_applied": true, "table_blending_enabled": true, "transparency_settings": {"default_color_transparency": 0.0, "meaningful_color_transparency": 0.7}}, "performance_info": {"parallel_enabled": true, "max_workers": 16, "turbo_jpeg_enabled": true, "turbo_jpeg_quality": 100, "turbo_jpeg_format": "png", "css_stability_enabled": true}, "inheritance_applied": {"font_family_changed": false, "font_size_changed": false, "alignment_changed": false, "padding_changed": false, "text_color_changed": false, "background_color_changed": false}, "css_render_info": {"table_position": {"left": 4101, "top": 224}, "background_dimensions": {"width": 9600.0, "height": 2160.0}, "crop_dimensions": {"width": 9600.0, "height": 2160.0}, "background_offset": {"x": 0, "y": 0}, "scaling_factors": {"max_scale_factor": 5.0, "content_area_shrink_ratio": 0.1}}, "csv_sampling_info": {"source_file": "assets/corpus/nl2sql_train/0011rows_batch_004.csv", "selected_columns": [54, 53, 114, 99], "selected_rows": [2, 6, 9, 5, 3], "csv_structure": {"total_columns": 116, "total_data_rows": 10}, "sampling_mode": "random"}, "resolved_key_params": {"table_size": {"header_rows": 1, "body_rows": 5, "cols": 4}, "content_source": "csv", "overflow_strategy": "wrap", "border_mode": "full", "zebra_stripes_enabled": false, "transparency_enabled": false, "random_seed": 2898690414}, "color_diff_info": {"statistics": {"total_borders_checked": 35, "color_diff_triggered": 0, "header_separator_affected": 0, "max_color_difference": 0, "min_color_difference": 0}, "triggered_borders": []}}